# -*- coding: utf-8 -*-

import os

from logger import Lo<PERSON>, INFO
from jss_api_extend.utils.file_utils import FileUtils
from singleton import Singleton


@Singleton
class Env(object):
    def __init__(self):
        self.logger = Logger(FileUtils.get_project_dir(), 'pd_test.log', INFO).log()
        self.task_logger = None
        self.mini_task_logger = None
        self.task_error_info = None

    def get_main_logger(self):
        return self.logger

    def get_env(self):
        return os.getenv('ENVIRONMENT', 'prod')

    def set_task_log(self, file_path):
        self.task_logger = Logger(file_path, INFO, is_task_log=True).log()

    def get_task_log(self):
        return self.task_logger

    def set_task_mini_log(self, file_path):
        self.mini_task_logger = Logger(file_path, INFO, is_task_log=True).log()

    def get_task_mini_log(self):
        return self.mini_task_logger

    def set_task_error_info(self, task_error_info):
        self.task_error_info = task_error_info

    def get_task_error_info(self):
        return self.task_error_info
    