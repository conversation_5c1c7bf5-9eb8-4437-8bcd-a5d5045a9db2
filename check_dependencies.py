#!/usr/bin/env python3
"""
项目依赖检查脚本
检查整个项目的依赖关系，包括：
1. 导入语句的有效性
2. 模块间的循环依赖
3. 外部包的可用性
4. 相对导入的正确性
"""

import os
import sys
import ast
import importlib
import traceback
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict, deque


class DependencyChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_root = self.project_root / "src"
        self.issues = []
        self.import_graph = defaultdict(set)
        self.external_imports = set()
        self.internal_modules = set()
        
    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []
        for root, dirs, files in os.walk(self.src_root):
            # 跳过__pycache__目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        return python_files
    
    def get_module_name(self, file_path: Path) -> str:
        """获取文件对应的模块名"""
        relative_path = file_path.relative_to(self.src_root)
        module_parts = list(relative_path.parts[:-1])  # 去掉文件名
        if relative_path.name != '__init__.py':
            module_parts.append(relative_path.stem)
        return '.'.join(module_parts)
    
    def parse_imports(self, file_path: Path) -> List[Tuple[str, str, int]]:
        """解析文件中的导入语句"""
        imports = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(('import', alias.name, node.lineno))
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    level = node.level
                    if level > 0:  # 相对导入
                        imports.append(('from_relative', f"{'.' * level}{module}", node.lineno))
                    else:  # 绝对导入
                        imports.append(('from_absolute', module, node.lineno))
                        
        except Exception as e:
            self.issues.append(f"解析文件失败 {file_path}: {e}")
            
        return imports
    
    def check_import_validity(self, file_path: Path, imports: List[Tuple[str, str, int]]):
        """检查导入语句的有效性"""
        current_module = self.get_module_name(file_path)
        
        for import_type, module_name, line_no in imports:
            try:
                if import_type == 'import':
                    # 直接导入模块
                    if module_name.startswith('jss_api_extend'):
                        self.internal_modules.add(module_name)
                        self.import_graph[current_module].add(module_name)
                    else:
                        self.external_imports.add(module_name)
                        # 尝试导入外部模块
                        try:
                            importlib.import_module(module_name)
                        except ImportError:
                            self.issues.append(f"外部模块不可用: {file_path}:{line_no} - {module_name}")
                            
                elif import_type == 'from_absolute':
                    # 绝对导入
                    if module_name.startswith('jss_api_extend'):
                        self.internal_modules.add(module_name)
                        self.import_graph[current_module].add(module_name)
                    else:
                        self.external_imports.add(module_name)
                        # 尝试导入外部模块
                        try:
                            importlib.import_module(module_name)
                        except ImportError:
                            self.issues.append(f"外部模块不可用: {file_path}:{line_no} - {module_name}")
                            
                elif import_type == 'from_relative':
                    # 相对导入
                    resolved_module = self.resolve_relative_import(current_module, module_name)
                    if resolved_module:
                        self.internal_modules.add(resolved_module)
                        self.import_graph[current_module].add(resolved_module)
                    else:
                        self.issues.append(f"相对导入解析失败: {file_path}:{line_no} - {module_name}")
                        
            except Exception as e:
                self.issues.append(f"检查导入失败 {file_path}:{line_no} - {module_name}: {e}")
    
    def resolve_relative_import(self, current_module: str, relative_import: str) -> str:
        """解析相对导入"""
        try:
            # 计算相对层级
            level = 0
            while relative_import.startswith('.'):
                level += 1
                relative_import = relative_import[1:]
            
            # 获取当前模块的父级
            current_parts = current_module.split('.')
            if level > len(current_parts):
                return None
                
            parent_parts = current_parts[:-level] if level > 0 else current_parts
            
            if relative_import:
                resolved = '.'.join(parent_parts + [relative_import])
            else:
                resolved = '.'.join(parent_parts)
                
            return resolved
            
        except Exception:
            return None
    
    def check_circular_dependencies(self):
        """检查循环依赖"""
        def has_cycle(graph):
            visited = set()
            rec_stack = set()
            
            def dfs(node):
                if node in rec_stack:
                    return True
                if node in visited:
                    return False
                    
                visited.add(node)
                rec_stack.add(node)
                
                for neighbor in graph.get(node, []):
                    if dfs(neighbor):
                        return True
                        
                rec_stack.remove(node)
                return False
            
            for node in graph:
                if node not in visited:
                    if dfs(node):
                        return True
            return False
        
        if has_cycle(self.import_graph):
            self.issues.append("检测到循环依赖")
            
            # 尝试找出具体的循环路径
            for module in self.import_graph:
                visited = set()
                path = []
                if self.find_cycle_path(module, visited, path):
                    self.issues.append(f"循环依赖路径: {' -> '.join(path)}")
                    break
    
    def find_cycle_path(self, node, visited, path):
        """查找循环依赖的具体路径"""
        if node in visited:
            cycle_start = path.index(node)
            return path[cycle_start:] + [node]
            
        visited.add(node)
        path.append(node)
        
        for neighbor in self.import_graph.get(node, []):
            result = self.find_cycle_path(neighbor, visited, path)
            if result:
                return result
                
        path.pop()
        visited.remove(node)
        return None
    
    def check_missing_files(self):
        """检查缺失的模块文件"""
        for module in self.internal_modules:
            module_path = self.src_root / module.replace('.', '/') 
            
            # 检查是否存在对应的.py文件或__init__.py
            py_file = module_path.with_suffix('.py')
            init_file = module_path / '__init__.py'
            
            if not py_file.exists() and not init_file.exists():
                self.issues.append(f"模块文件不存在: {module} (期望路径: {py_file} 或 {init_file})")
    
    def generate_dependency_report(self):
        """生成依赖关系报告"""
        report = []
        report.append("=" * 60)
        report.append("项目依赖检查报告")
        report.append("=" * 60)
        
        # 基本统计
        python_files = self.find_python_files()
        report.append(f"\n📊 基本统计:")
        report.append(f"  Python文件数量: {len(python_files)}")
        report.append(f"  内部模块数量: {len(self.internal_modules)}")
        report.append(f"  外部依赖数量: {len(self.external_imports)}")
        
        # 外部依赖列表
        if self.external_imports:
            report.append(f"\n📦 外部依赖:")
            for ext_import in sorted(self.external_imports):
                report.append(f"  - {ext_import}")
        
        # 问题列表
        if self.issues:
            report.append(f"\n❌ 发现的问题 ({len(self.issues)} 个):")
            for i, issue in enumerate(self.issues, 1):
                report.append(f"  {i}. {issue}")
        else:
            report.append(f"\n✅ 未发现依赖问题!")
        
        # 模块依赖图
        if self.import_graph:
            report.append(f"\n🔗 模块依赖关系:")
            for module, deps in sorted(self.import_graph.items()):
                if deps:
                    report.append(f"  {module}:")
                    for dep in sorted(deps):
                        report.append(f"    └── {dep}")
        
        return '\n'.join(report)
    
    def run_check(self):
        """运行完整的依赖检查"""
        print("🔍 开始检查项目依赖...")
        
        # 添加src目录到Python路径
        if str(self.src_root) not in sys.path:
            sys.path.insert(0, str(self.src_root))
        
        python_files = self.find_python_files()
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        # 解析所有导入
        for file_path in python_files:
            print(f"📄 检查文件: {file_path.relative_to(self.project_root)}")
            imports = self.parse_imports(file_path)
            self.check_import_validity(file_path, imports)
        
        # 检查循环依赖
        print("🔄 检查循环依赖...")
        self.check_circular_dependencies()
        
        # 检查缺失文件
        print("📋 检查缺失的模块文件...")
        self.check_missing_files()
        
        # 生成报告
        report = self.generate_dependency_report()
        
        # 保存报告到文件
        report_file = self.project_root / "dependency_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📋 报告已保存到: {report_file}")
        print("\n" + report)
        
        return len(self.issues) == 0


def main():
    """主函数"""
    # 获取项目根目录
    project_root = os.getcwd()
    
    print(f"🚀 项目依赖检查工具")
    print(f"📂 项目根目录: {project_root}")
    
    checker = DependencyChecker(project_root)
    success = checker.run_check()
    
    if success:
        print("\n✅ 依赖检查通过!")
        sys.exit(0)
    else:
        print(f"\n❌ 发现 {len(checker.issues)} 个依赖问题!")
        sys.exit(1)


if __name__ == "__main__":
    main()
