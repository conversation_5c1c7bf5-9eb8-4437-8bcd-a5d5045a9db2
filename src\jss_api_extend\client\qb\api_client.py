# -*- coding: utf-8 -*-

import json
import sys
from typing import Dict, Any, Optional

import requests
import time

from jss_api_extend.client.qb.models import SortOrder

# 设置默认编码为 UTF-8
from jss_api_extend.utils.string_utils import StringUtils

if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

from jss_api_extend.client.qb.models import (
    Platform,
    ApiConfig,
    ArticleDetail,
    CommentResponse,
    ApiResponse,
    DouyinGroupResponse,
    WxVideoGroupResponse,
    XiaohongshuGroupResponse
)


class ApiClient:
    def __init__(self, config: ApiConfig, logger_):
        self.config = config
        self.logger = logger_

    def _make_request(self, router: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a POST request to the API using form-data
        
        Args:
            router: API route path
            params: Parameters to be passed in the request
            
        Returns:
            API response as dictionary
        """
        # 准备 form-data 数据
        form_data = {
            "project_id": str(self.config.project_id),
            "sign": self.config.sign,
            "router": router,
            "params": json.dumps(params, ensure_ascii=False)
        }

        print(f"\nDebug - Request URL: {self.config.base_url}")
        print(f"Debug - Form Data: {form_data}")

        response = requests.post(
            self.config.base_url,
            data=form_data
        )
        response.encoding = 'utf-8'

        print(f"Debug - Response Status Code: {response.status_code}")
        print(f"Debug - Response Text: {response.text[:500]}...")  # Print first 500 chars

        # Don't raise exception, just return the response
        return response.json()

    def get_article_detail(self, platform: Platform, news_url: str) -> ArticleDetail:
        """
        Get article details from the API
        
        Args:
            platform: Platform enum (xhs, dy, or wxvideo)
            news_url: URL of the article or eid for wxvideo
            
        Returns:
            ArticleDetail object containing article information
        """
        params = {
            "platform": platform.value,
            "news_url": news_url
        }

        response = self._make_request("/jss/api/get-detail", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success or api_response.code != 200:
            self.logger.error(f"API请求失败: {api_response.message}")
            raise Exception(f"API请求失败: {api_response.message}")

        return ArticleDetail.from_dict(api_response.data)

    def _get_comments(self, platform: Platform, news_url: str, cursor: Optional[str] = None) -> CommentResponse:
        """
        Get comments for an article
        
        Args:
            platform: Platform enum (xhs, dy, or wxvideo)
            news_url: URL of the article or eid for wxvideo
            cursor: Pagination cursor (optional)
            
        Returns:
            CommentResponse object containing comments and pagination info
        """
        params = {
            "platform": platform.value,
            "news_url": news_url
        }

        if cursor:
            params["cursor"] = cursor

        response = self._make_request("/jss/api/get-comment", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success or api_response.code != 200:
            raise Exception(f"API请求失败: {api_response.message}")

        return CommentResponse.from_dict(api_response.data)

    def _get_sub_comments(self, platform: Platform, first_cid:str, news_url: str, cursor: Optional[str] = None) -> CommentResponse:
        """
        Get comments for an article

        Args:
            platform: Platform enum (xhs, dy, or wxvideo)
            news_url: URL of the article or eid for wxvideo
            cursor: Pagination cursor (optional)

        Returns:
            CommentResponse object containing comments and pagination info
        """
        params = {
            "platform": platform.value,
            "news_url": news_url,
            "first_cid": first_cid
        }

        if cursor:
            params["cursor"] = cursor

        response = self._make_request("/jss/api/get-comment", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success or api_response.code != 200:
            raise Exception(f"API请求失败: {api_response.message}")

        return CommentResponse.from_dict(api_response.data)

    def get_article_comments(self, platform: Platform, news_url: str, count: int) -> list:
        comment_list = []
        cursor = None
        has_more = True
        try:
            page = 1
            while has_more:
                response: CommentResponse = self._get_comments(platform=platform, news_url=news_url, cursor=cursor)
                self.logger.info("get_article_comments response, platform=%s news_url=%s response=%s"
                                 % (platform, news_url, StringUtils.obj_2_json_string(response)))
                new_comments = response.comments
                cursor = response.cursor
                has_more = response.has_more

                comment_list.extend(new_comments)
                page += 1

                if count <= len(comment_list):
                    break
        except Exception as e:
            self.logger.error("get_one_douyin_articles error, %s", e)

        return comment_list

    def get_article_sub_comments(self, platform: Platform, news_url) -> list:
        comment_list = []
        cursor = None
        has_more = True
        try:
            while has_more:
                response: CommentResponse = self._get_sub_comments(platform=platform, news_url=news_url, cursor=cursor)
                self.logger.info("get_article_comments response, platform=%s news_url=%s response=%s"
                                 % (platform, news_url, StringUtils.obj_2_json_string(response)))
                new_comments = response.comments
                cursor = response.cursor
                has_more = response.has_more

                comment_list.extend(new_comments)
        except Exception as e:
            self.logger.error("get_one_douyin_articles error, %s", e)

        return comment_list

    def _get_douyin_group_articles(
            self,
            day: str,
            douyin_code: Optional[str] = None,
            order: SortOrder = SortOrder.DESC,
            sort: str = "publish_time",
            limit: int = 50,
            page: int = 1
    ) -> DouyinGroupResponse:
        """
        获取抖音自定义榜单分组内文章列表
        
        Args:
            day: 筛选日期（示例：2022-08-18）
            douyin_code: 抖音号
            order: 排序类型 asc,desc
            sort: 排序字段：发布时间publish_time, 评论数comment_count，点赞数like_count
            limit: 每页条数
            page: 页码
            
        Returns:
            DouyinGroupResponse object containing articles and pagination info
        """
        params = {
            "day": day,
            "order": order.value,
            "sort": sort,
            "limit": limit,
            "page": page
        }

        if douyin_code:
            params["douyin_code"] = douyin_code

        response = self._make_request("/myrank/douyin/group-article", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success:
            raise Exception(f"API请求失败: {api_response.message}")

        return DouyinGroupResponse.from_dict(response['data'])

    def get_all_douyin_articles(self, day: str) -> list:
        limit = 50
        article_list = []
        try:
            page = 1
            while True:
                response: DouyinGroupResponse = self._get_douyin_group_articles(day=day, limit=limit, page=page)
                self.logger.info("get_one_douyin_articles response, day=%s response=%s" %(day, StringUtils.obj_2_json_string(response)))

                new_articles = response.articles
                if new_articles is None or len(new_articles) == 0:
                    break

                article_list.extend(new_articles)
                page += 1

                time.sleep(2)  # 延迟 2 秒
        except Exception as e:
            self.logger.error("get_one_douyin_articles error, %s", e)

        return article_list

    def get_all_xhs_articles(self, day: str) -> list:
        limit = 50
        article_list = []
        try:
            page = 1
            while True:
                response: XiaohongshuGroupResponse = self._get_xiaohongshu_group_articles(
                    day=day, limit=limit, page=page)
                self.logger.info("get_all_xhs_articles response, day=%s response=%s" %(day, StringUtils.obj_2_json_string(response)))

                new_articles = response.articles
                if new_articles is None or len(new_articles) == 0:
                    break

                article_list.extend(new_articles)
                page += 1

                time.sleep(2)  # 延迟 2 秒
        except Exception as e:
            self.logger.error("get_all_xhs_articles error, %s", e)

        return article_list

    def get_all_wxvideo_articles(self, day: str) -> list:
        limit = 50
        article_list = []
        try:
            page = 1
            while True:
                response: WxVideoGroupResponse = self._get_wxvideo_group_articles(
                    day=day, limit=limit, page=page)
                self.logger.info("get_all_wxvideo_articles response, day=%s response=%s" %(day, StringUtils.obj_2_json_string(response)))

                new_articles = response.articles
                if new_articles is None or len(new_articles) == 0:
                    break

                article_list.extend(new_articles)
                page += 1

                time.sleep(2)  # 延迟 2 秒
        except Exception as e:
            self.logger.error("get_all_wxvideo_articles error, %s", e)

        return article_list

    def _get_wxvideo_group_articles(
            self,
            day: str,
            wxvideo_id: Optional[str] = None,
            order: SortOrder = SortOrder.DESC,
            sort: str = "posttime",
            limit: int = 20,
            page: int = 1
    ) -> WxVideoGroupResponse:
        """
        获取微信视频号自定义榜单分组内文章列表
        
        Args:
            day: 筛选日期（示例：2022-08-18）
            wxvideo_id: 微信视频号ID
            order: 排序类型 asc,desc
            sort: 排序字段：发布时间posttime, 评论数comment_num，点赞数like_num
            limit: 每页条数
            page: 页码
            
        Returns:
            WxVideoGroupResponse object containing articles and pagination info
        """
        params = {
            "day": day,
            "order": order.value,
            "sort": sort,
            "limit": limit,
            "page": page
        }

        if wxvideo_id:
            params["wxvideo_id"] = wxvideo_id

        response = self._make_request("/myrank/wxvideo/group-article", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success:
            raise Exception(f"API请求失败: {api_response.message}")

        return WxVideoGroupResponse.from_dict(response['data'])

    def get_one_wxvideo_articles(self, day: str, douyin_code: str) -> list:
        limit = 50
        article_list = []
        try:
            page = 1
            while True:
                response: WxVideoGroupResponse = self._get_wxvideo_group_articles(day=day, wxvideo_id=douyin_code, limit=limit, page=page)
                self.logger.info("get_one_wxvideo_articles response, day=%s douyin_code=%s response=%s" %(day, douyin_code, StringUtils.obj_2_json_string(response)))

                new_articles = response.articles
                if new_articles is None or len(new_articles) == 0:
                    break

                article_list.extend(new_articles)
                page += 1
        except Exception as e:
            self.logger.error("get_one_wxvideo_articles error, %s", e)

        return article_list

    def _get_xiaohongshu_group_articles(
            self,
            day: str,
            xiaohongshu_id: Optional[str] = None,
            order: SortOrder = SortOrder.DESC,
            sort: str = "posttime",
            limit: int = 20,
            page: int = 1
    ) -> XiaohongshuGroupResponse:
        """
        获取小红书自定义榜单分组内文章列表
        
        Args:
            day: 筛选日期（示例：2022-08-18）
            xiaohongshu_id: 小红书ID
            order: 排序类型 asc,desc
            sort: 排序字段：发布时间posttime, 评论数news_comment_count，阅读数read_num
            limit: 每页条数
            page: 页码
            
        Returns:
            XiaohongshuGroupResponse object containing articles and pagination info
        """
        params = {
            "day": day,
            "order": order.value,
            "sort": sort,
            "limit": limit,
            "page": page
        }

        if xiaohongshu_id:
            params["xiaohongshu_id"] = xiaohongshu_id

        response = self._make_request("/myrank/xiaohongshu/group-article", params)
        api_response = ApiResponse.from_dict(response)

        if not api_response.success:
            raise Exception(f"API请求失败: {api_response.message}")

        return XiaohongshuGroupResponse.from_dict(response['data'])

    def get_one_xhs_articles(self, day: str, douyin_code: str) -> list:
        limit = 50
        article_list = []
        try:
            page = 1
            while True:
                response: XiaohongshuGroupResponse = self._get_xiaohongshu_group_articles(day=day, xiaohongshu_id=douyin_code, limit=limit, page=page)
                self.logger.info("get_one_xhs_articles response, day=%s douyin_code=%s response=%s" %(day, douyin_code, StringUtils.obj_2_json_string(response)))

                new_articles = response.articles
                if new_articles is None or len(new_articles) == 0:
                    break

                article_list.extend(new_articles)
                page += 1
        except Exception as e:
            self.logger.error("get_one_wxvideo_articles error, %s", e)

        return article_list
