[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jss-api-extend"
version = "0.1.2"
description = "JSS API Extension - A comprehensive Python package for social media platform APIs (Douyin, Xiaohongshu, Kuaishou) and DingTalk Bitable integration"
readme = "README.md"
requires-python = ">=3.8"
authors = [
    {name = "JSS Team", email = "<EMAIL>"}
]
keywords = ["api", "social-media", "douyin", "xiaohongshu", "kuaishou", "dingtalk", "bitable"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "requests>=2.25.0",
    "python-dateutil>=2.8.0",
    "tikhub>=1.0.0",
    "lark-oapi>=1.0.0",
    "alibabacloud-dingtalk>=2.0.0",
    "alibabacloud-tea-util>=0.3.0",
    "alibabacloud-tea-openapi>=0.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-asyncio>=0.18.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
]
test = [
    "pytest>=6.0",
    "pytest-asyncio>=0.18.0",
    "pytest-cov>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/jinsuosuo/jss-api-extend"
Documentation = "https://github.com/jinsuosuo/jss-api-extend#readme"
Repository = "https://github.com/jinsuosuo/jss-api-extend.git"
"Bug Tracker" = "https://github.com/jinsuosuo/jss-api-extend/issues"

[tool.setuptools]
package-dir = {"" = "src"}
license-files = []

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.txt"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["jss_api_extend"]
known_third_party = ["tikhub", "lark_oapi", "alibabacloud_dingtalk", "dateutil", "requests"]

# mypy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "tikhub.*",
    "lark_oapi.*",
    "alibabacloud_dingtalk.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--color=yes",
]
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Flake8 configuration (in setup.cfg format, but documented here)
# [flake8]
# max-line-length = 88
# extend-ignore = E203, W503
# exclude = .git,__pycache__,build,dist,.eggs



