import json
from typing import List, Optional

from jss_api_extend.client.jzl_wxvideo_cllient import JZLWxVideoClient
from jss_api_extend.adapter.models.wxvideo_models import WxVideoModel


class WxVideoHandler(object):

    def __init__(self, logger_, api_key: str = ""):
        self.logger_ = logger_
        self.jzl_client = JZLWxVideoClient(api_key)

    def query_user_video(self, v2_name: str, pages: int = 1) -> List[WxVideoModel]:
        """
        查询用户视频列表

        :param v2_name: 微信视频号用户名
        :param pages: 翻几页
        :return: WxVideoModel 列表
        """
        return self._query_user_video_by_jzl(v2_name, pages)
    


    def _query_user_video_by_jzl(self, v2_name: str, pages: int = 1) -> List[WxVideoModel]:
        """
         极致了 查询用户视频列表

        :param v2_name: 微信视频号用户名
        :param pages: 翻几页
        :return: WxVideoModel 列表
        """

        if v2_name is None:
            self.logger_.error("query_user_video failed, v2_name is None")
            return []

        all_videos = []
        last_buffer = ""

        try:
            for page in range(pages):
                self.logger_.info(f"正在获取第 {page + 1} 页数据，v2_name={v2_name}")

                # 调用 JZL API
                response_data, is_failed = self.jzl_client.query_author_article_list(
                    v2_name=v2_name,
                    last_buffer=last_buffer
                )

                if is_failed or response_data is None:
                    self.logger_.error(f"获取第 {page + 1} 页数据失败")
                    break

                # 检查响应状态
                if response_data.get('code') != 0:
                    self.logger_.error(f"API返回错误，code={response_data.get('code')}")
                    break

                # 解析视频数据
                video_objects = response_data.get('object', [])
                if not video_objects:
                    self.logger_.info(f"第 {page + 1} 页没有更多数据")
                    break

                # 转换为 WxVideoModel
                page_videos = []
                for video_data in video_objects:
                    try:
                        video_model = JzlWxVideoModelHelper.cover_json_wxvideo_model(video_data)
                        if video_model:
                            page_videos.append(video_model)
                    except Exception as e:
                        self.logger_.error(f"转换视频数据失败: {e}, data={video_data}")

                if
                all_videos.extend(page_videos)
                self.logger_.info(f"第 {page + 1} 页获取到 {len(page_videos)} 个视频")

                # 更新 last_buffer 用于下一页
                last_buffer = response_data.get('last_buffer', '')

                # 检查是否还有下一页
                continue_flag = response_data.get('continue_flag', 0)
                if continue_flag == 0:
                    self.logger_.info("已获取所有数据")
                    break

        except Exception as e:
            self.logger_.error(f"query_user_video 异常: {e}")

        self.logger_.info(f"总共获取到 {len(all_videos)} 个视频")
        return all_videos


class JzlWxVideoModelHelper:
    """
    主要处理 JSON 转 model
    """

    @staticmethod
    def cover_json_wxvideo_model(data: dict) -> Optional[WxVideoModel]:
        """
        将 JZL API 返回的 JSON 数据转换为 WxVideoModel

        :param data: API 返回的单个视频数据
        :return: WxVideoModel 实例或 None
        """
        try:
            return WxVideoModel(
                object_id=data.get('object_id', ''),
                object_nonce_id=data.get('object_nonce_id', ''),
                media_type=data.get('media_type', ''),
                live_info=data.get('live_info', {}),
                fav_count=data.get('fav_count', 0),
                like_count=data.get('like_count', 0),
                forward_count=data.get('forward_count', 0),
                comment_count=data.get('comment_count', 0),
                title=data.get('title', ''),
                download_url=data.get('download_url', ''),
                publish_time=data.get('publish_time', ''),
                file_size=data.get('file_size', 0),
                video_play_len=data.get('video_play_len', 0),
                cover_url=data.get('cover_url', ''),
                export_id=data.get('export_id', '')
            )
        except Exception as e:
            # 记录错误但不抛出异常，返回 None
            print(f"转换 WxVideoModel 失败: {e}")
            return None