from typing import List, Optional
from datetime import datetime

from jss_api_extend.adapter.models.wxvideo_models import WxVideoModel
from jss_api_extend.client.jzl_wxvideo_cllient import JZLWxVideoClient


class WxVideoHandler(object):

    def __init__(self, logger_, api_key: str = "JZLb72fba8ae490c68f"):
        self.logger_ = logger_
        self.jzl_client = JZLWxVideoClient(api_key)

    def query_work_tracing(self, v2_name: str, pages: int = 1) -> List[WxVideoModel]:
        """
        查询用户视频列表

        :param v2_name: 微信视频号用户名
        :param pages: 翻几页
        :return: WxVideoModel 列表
        """
        videos, need_retry = self._query_user_video_by_jzl(v2_name, pages)
        if need_retry:
            self.logger_.error(f"query_user_video failed, need retry, v2_name={v2_name}")

        return videos

    def _query_work_tracing_by_jzl(self, v2_name: str, start_time: int) -> tuple[List[WxVideoModel], bool]:
        """
        极致了 查询用户视频列表

        账号历史作品追溯 从 start_time ~ 当前
        注意
        最前页为置顶作品，要检查置顶作品（is_top = 1）是否处于时间区间内,如果是加入返回结果列表,
        继续检查，到当前页面结束前如果查询到数据出现发布时间 < start_time， t停止翻页，将json转为 对象返回
        时间格式为 传入为时间戳

        "publish_time": "2025-07-14 18:00:04",
        :param v2_name: 微信视频号用户名
        :param start_time: 开始时间戳
        :return: (WxVideoModel 列表, 是否需要重试)
        """

        if v2_name is None:
            self.logger_.error("_query_work_tracing_by_jzl failed, v2_name is None")
            return [], False

        result_videos = []
        last_buffer = ""
        need_retry = False
        has_more = True
        is_first_page = True
        processed_count = 0

        try:
            while has_more:
                self.logger_.info(f"正在获取用户作品追溯数据，v2_name={v2_name}, last_buffer={last_buffer}")

                response_data, is_failed = self.jzl_client.query_author_article_list(
                    v2_name=v2_name,
                    last_buffer=last_buffer
                )

                if is_failed or response_data is None:
                    self.logger_.error("获取数据失败")
                    need_retry = True
                    break

                if response_data.get('code') != 0:
                    self.logger_.error(f"API返回错误，code={response_data.get('code')}")
                    break

                video_objects = response_data.get('object', [])
                if not video_objects:
                    self.logger_.info("没有更多数据")
                    break


                should_stop = False
                for i, video_data in enumerate(video_objects):
                    try:
                        publish_time_str = video_data.get('publish_time', '')

                        # 将发布时间字符串转换为时间戳
                        publish_timestamp = 0
                        if publish_time_str:
                            try:
                                dt = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S")
                                publish_timestamp = int(dt.timestamp())
                            except ValueError as e:
                                self.logger_.error(f"时间格式解析失败: {publish_time_str}, error: {e}")
                                continue

                        # 推断是否为置顶作品：前3个作品且是第一页
                        current_position = processed_count + i
                        is_top_inferred = is_first_page and current_position < 3

                        # 不判置顶作品的时间
                        if is_top_inferred:
                            if publish_timestamp >= start_time:
                                self.logger_.info(f"找到符合条件的置顶作品(推断)，position={current_position}, publish_time={publish_time_str}")
                                video_model = JzlWxVideoModelHelper.cover_json_wxvideo_model(video_data)
                                if video_model:
                                    result_videos.append(video_model)
                            else:
                                self.logger_.info(f"置顶作品(推断)不在时间区间内，position={current_position}, publish_time={publish_time_str}")
                            continue


                        if publish_timestamp < start_time:
                            self.logger_.info(f"发现作品发布时间早于开始时间，停止翻页。publish_time={publish_time_str}, start_time={start_time}")
                            should_stop = True
                            break

                        self.logger_.info(f"找到符合条件的作品，publish_time={publish_time_str}")
                        video_model = JzlWxVideoModelHelper.cover_json_wxvideo_model(video_data)
                        if video_model:
                            result_videos.append(video_model)

                    except Exception as e:
                        self.logger_.error(f"处理视频数据失败: {e}, data={video_data}")

                processed_count += len(video_objects)
                is_first_page = False

                if should_stop:
                    break

                continue_flag = response_data.get('continue_flag', 0)
                if continue_flag == 0:
                    self.logger_.info("没有更多页面")
                    has_more = False
                else:

                    last_buffer = response_data.get('last_buffer', '')
                    if not last_buffer:
                        self.logger_.info("last_buffer为空，停止翻页")
                        break

        except Exception as e:
            self.logger_.error(f"查询用户作品追溯异常: {e}")
            need_retry = True

        self.logger_.info(f"用户作品追溯完成，共获取到 {len(result_videos)} 个作品")
        return result_videos, need_retry

    def query_user_video(self, v2_name: str, pages: int = 1) -> List[WxVideoModel]:
        """
        查询用户视频列表

        :param v2_name: 微信视频号用户名
        :param pages: 翻几页
        :return: WxVideoModel 列表
        """
        videos, need_retry = self._query_user_video_by_jzl(v2_name, pages)
        if need_retry:
            self.logger_.error(f"query_user_video failed, need retry, v2_name={v2_name}")

        return videos

    def _query_user_video_by_jzl(self, v2_name: str, pages: int = 1) -> tuple[List[WxVideoModel], bool]:
        """
         极致了 查询用户视频列表

        :param v2_name: 微信视频号用户名
        :param pages: 翻几页
        :return: (WxVideoModel 列表, 是否需要重试)
        """

        if v2_name is None:
            self.logger_.error("query_user_video failed, v2_name is None")
            return [], False

        all_videos = []
        last_buffer = ""
        need_retry = False

        try:
            for page in range(pages):
                self.logger_.info(f"正在获取第 {page + 1} 页数据，v2_name={v2_name}")

                # 调用 JZL API
                response_data, is_failed = self.jzl_client.query_author_article_list(
                    v2_name=v2_name,
                    last_buffer=last_buffer
                )

                if is_failed or response_data is None:
                    self.logger_.error(f"获取第 {page + 1} 页数据失败")
                    need_retry = True
                    break

                # 检查响应状态
                if response_data.get('code') != 0:
                    self.logger_.error(f"API返回错误，code={response_data.get('code')}")
                    break

                # 解析视频数据
                video_objects = response_data.get('object', [])
                if not video_objects:
                    self.logger_.info(f"第 {page + 1} 页没有更多数据")
                    break

                # 转换为 WxVideoModel
                page_videos = []
                for video_data in video_objects:
                    try:
                        video_model = JzlWxVideoModelHelper.cover_json_wxvideo_model(video_data)
                        if video_model:
                            page_videos.append(video_model)
                    except Exception as e:
                        self.logger_.error(f"转换视频数据失败: {e}, data={video_data}")

                all_videos.extend(page_videos)
                self.logger_.info(f"第 {page + 1} 页获取到 {len(page_videos)} 个视频")

                last_buffer = response_data.get('last_buffer', '')

                continue_flag = response_data.get('continue_flag', 0)
                if continue_flag == 0:
                    self.logger_.info("已获取所有数据")
                    break

        except Exception as e:
            self.logger_.error(f"query_user_video 异常: {e}")
            need_retry = True

        self.logger_.info(f"总共获取到 {len(all_videos)} 个视频")
        return all_videos, need_retry



class JzlWxVideoModelHelper:
    """
    主要处理 JSON 转 model
    """

    @staticmethod
    def cover_json_wxvideo_model(data: dict) -> Optional[WxVideoModel]:
        """
        将 JZL API 返回的 JSON 数据转换为 WxVideoModel

        :param data: API 返回的单个视频数据
        :return: WxVideoModel 实例或 None
        """
        try:
            return WxVideoModel(
                object_id=data.get('object_id', ''),
                object_nonce_id=data.get('object_nonce_id', ''),
                media_type=data.get('media_type', ''),
                live_info=data.get('live_info', {}),
                fav_count=data.get('fav_count', 0),
                like_count=data.get('like_count', 0),
                forward_count=data.get('forward_count', 0),
                comment_count=data.get('comment_count', 0),
                title=data.get('title', ''),
                download_url=data.get('download_url', ''),
                publish_time=data.get('publish_time', ''),
                file_size=data.get('file_size', 0),
                video_play_len=data.get('video_play_len', 0),
                cover_url=data.get('cover_url', ''),
                export_id=data.get('export_id', '')
            )
        except Exception as e:
            print(f"转换 WxVideoModel 失败: {e}")
            return None
