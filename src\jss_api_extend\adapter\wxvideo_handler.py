import json

from jss_api_extend.client.jzl_wxvideo_cllient import JZLWxVideoClient


class WxVideoHandler(object):

    def __init__(self, logger_):
        self.logger_ = logger_
        self.jzl_client = JZLWxVideoClient("")


    def query_user_video(self, v2_name: str, pages: int = 1) -> List[]:
        """

        :param v2_name:
        :param pages: 翻几页
        :return:
        """
        if v2_name is None:
            self.logger_.error("query_user_video none , v2_name is None")

        self.jzl_client.query_author_article_list(v2_name = v2_name,last_buffer= )





class JzlWxVideoModelHelper:
    """
    主要处理 JSON 转 model
    """

    @staticmethod
    def cover_json_wxvideo_model(data: dict):


