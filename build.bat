@echo off
REM JSS API Extension - Windows Build Script
REM 这个脚本提供了与 Makefile 相同的功能，适用于 Windows 系统

setlocal enabledelayedexpansion

if "%1"=="" (
    echo Available commands:
    echo   build.bat help          Show this help
    echo   build.bat clean         Clean build artifacts
    echo   build.bat format        Format code
    echo   build.bat lint          Run linting
    echo   build.bat test          Run tests
    echo   build.bat build         Build package
    echo   build.bat upload-test   Upload to Test PyPI
    echo   build.bat upload        Upload to PyPI
    echo   build.bat check         Run all checks
    echo   build.bat dev           Development cycle
    goto :eof
)

if "%1"=="help" (
    echo JSS API Extension - Windows Build Script
    echo.
    echo Available commands:
    echo   clean         Clean build artifacts
    echo   format        Format code with black and isort
    echo   lint          Run linting with flake8 and mypy
    echo   test          Run tests with pytest
    echo   build         Build the package
    echo   upload-test   Upload to Test PyPI
    echo   upload        Upload to PyPI
    echo   check         Run format-check, lint, and test
    echo   dev           Run format, lint, and test
    goto :eof
)

if "%1"=="clean" (
    echo Cleaning build artifacts...
    rmdir /s /q build 2>nul
    rmdir /s /q dist 2>nul
    for /d %%i in (*.egg-info) do rmdir /s /q "%%i" 2>nul
    rmdir /s /q src\jss_api_extend.egg-info 2>nul
    rmdir /s /q .pytest_cache 2>nul
    rmdir /s /q .mypy_cache 2>nul
    del /q .coverage 2>nul
    rmdir /s /q htmlcov 2>nul
    for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
    for /r . %%f in (*.pyc) do @if exist "%%f" del /q "%%f"
    echo Clean complete!
    goto :eof
)

if "%1"=="format" (
    echo Formatting code...
    python -m black src tests examples
    python -m isort src tests examples
    echo Format complete!
    goto :eof
)

if "%1"=="lint" (
    echo Running linting...
    python -m flake8 src tests examples
    if !errorlevel! neq 0 (
        echo Flake8 failed!
        exit /b 1
    )
    python -m mypy src
    if !errorlevel! neq 0 (
        echo MyPy failed!
        exit /b 1
    )
    echo Linting complete!
    goto :eof
)

if "%1"=="test" (
    echo Running tests...
    python -m pytest
    if !errorlevel! neq 0 (
        echo Tests failed!
        exit /b 1
    )
    echo Tests complete!
    goto :eof
)

if "%1"=="build" (
    echo Building package...
    call :clean
    python -m build
    if !errorlevel! neq 0 (
        echo Build failed!
        exit /b 1
    )
    echo Checking package...
    python -m twine check dist\*
    if !errorlevel! neq 0 (
        echo Package check failed!
        exit /b 1
    )
    echo Build complete!
    goto :eof
)

if "%1"=="upload-test" (
    echo Uploading to Test PyPI...
    call :build
    if !errorlevel! neq 0 exit /b 1
    python -m twine upload --repository testpypi dist\*
    if !errorlevel! neq 0 (
        echo Upload to Test PyPI failed!
        exit /b 1
    )
    echo Upload to Test PyPI complete!
    goto :eof
)

if "%1"=="upload" (
    echo Uploading to PyPI...
    call :build
    if !errorlevel! neq 0 exit /b 1
    python -m twine upload dist\*
    if !errorlevel! neq 0 (
        echo Upload to PyPI failed!
        exit /b 1
    )
    echo Upload to PyPI complete!
    goto :eof
)

if "%1"=="check" (
    echo Running all checks...
    echo Checking code format...
    python -m black --check src tests examples
    if !errorlevel! neq 0 (
        echo Code format check failed!
        exit /b 1
    )
    python -m isort --check-only src tests examples
    if !errorlevel! neq 0 (
        echo Import sort check failed!
        exit /b 1
    )
    call :lint
    if !errorlevel! neq 0 exit /b 1
    call :test
    if !errorlevel! neq 0 exit /b 1
    echo All checks passed!
    goto :eof
)

if "%1"=="dev" (
    echo Running development cycle...
    call :format
    call :lint
    if !errorlevel! neq 0 exit /b 1
    call :test
    if !errorlevel! neq 0 exit /b 1
    echo Development cycle complete!
    goto :eof
)

echo Unknown command: %1
echo Use "build.bat help" to see available commands.
exit /b 1

:clean
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
for /d %%i in (*.egg-info) do rmdir /s /q "%%i" 2>nul
rmdir /s /q src\jss_api_extend.egg-info 2>nul
goto :eof
