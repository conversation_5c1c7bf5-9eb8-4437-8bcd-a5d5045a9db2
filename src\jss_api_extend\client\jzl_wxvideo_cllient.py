import json

import requests
import time
from typing import Dict, Any, Optional, Tuple


class JZLWxVideoClient:

    API_URL = "https://www.dajiala.com/fbmain/monitor/v3/wxvideo"

    ERROR_CODES = {
        101: "v2_name 错误请检查",
        102: "last_buffer错误请检查",
        103: "获取列表失败",
        105: "请求类型不存在",
        20001: "金额不足",
        50000: "服务器内部错误"
    }

    def __init__(self, key: str = None):
        """
        初始化客户端。

        Args:
            key (str, optional): 您的极致了API key。可以在初始化时设置，也可以在调用时传入。
        """
        self.default_params = {}
        if key:
            self.default_params['key'] = key

    def query_author_article_list(self, **kwargs) -> Tuple[Optional[Dict[str, Any]], bool]:
        """
        调用接口获取视频列表。

        Args:
            **kwargs: 接口所需的参数，例如 v2_name, last_buffer, key 等。

        Returns:
            一个元组，包含两个值，可直接解包:
            - (dict or None): 成功时为API返回的JSON数据，若所有重试都失败则为None。
            - (bool): 如果所有重试都失败了，则为 True，否则为 False。
        """
        self.default_params["type"] = 1

        params = {**self.default_params, **kwargs}

        max_retries = 2

        for attempt in range(max_retries + 1):
            try:
                print(f"--- 正在进行第 {attempt + 1} 次尝试... ---")

                response = requests.post(self.API_URL, data=params, timeout=15)
                response.raise_for_status()

                # 解析返回的JSON数据
                response_data = response.json()
                code = response_data.get('code')

                if code == 0:
                    # 调用成功
                    print("API调用成功！")
                    return response_data, False

                elif code == 50000:
                    print(f"尝试失败，错误码: {code} ({self.ERROR_CODES.get(code, '未知错误')})。")
                    if attempt < max_retries:
                        print("将在1秒后重试...")
                        time.sleep(1)
                    continue

                else:
                    error_message = self.ERROR_CODES.get(code, "未知的API错误")
                    print(f"API返回明确错误，无需重试。错误码: {code}, 信息: {error_message}")
                    return response_data, False

            except requests.exceptions.RequestException as e:
                print(f"发生网络请求异常: {e}")
                if attempt < max_retries:
                    print("将在1秒后重试...")
                    time.sleep(1)

            except ValueError:
                print("无法解析返回的JSON数据。")
                if attempt < max_retries:
                    print("将在1秒后重试...")
                    time.sleep(1)

        print("所有重试尝试均告失败。")
        return None, True
