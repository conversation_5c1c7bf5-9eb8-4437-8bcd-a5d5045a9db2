# JSS API Extension - PowerShell Build Script
# 这个脚本提供了与 Makefile 相同的功能，适用于 Windows PowerShell

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

function Show-Help {
    Write-Host "JSS API Extension - PowerShell Build Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor Yellow
    Write-Host "  help          Show this help"
    Write-Host "  clean         Clean build artifacts"
    Write-Host "  format        Format code with black and isort"
    Write-Host "  lint          Run linting with flake8 and mypy"
    Write-Host "  test          Run tests with pytest"
    Write-Host "  build         Build the package"
    Write-Host "  upload-test   Upload to Test PyPI"
    Write-Host "  upload        Upload to PyPI"
    Write-Host "  check         Run all checks"
    Write-Host "  dev           Development cycle"
    Write-Host ""
    Write-Host "Usage: .\build.ps1 <command>" -ForegroundColor Cyan
}

function Invoke-Clean {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow

    $itemsToRemove = @("build", "dist", "*.egg-info", ".pytest_cache", ".mypy_cache", ".coverage", "htmlcov")

    foreach ($item in $itemsToRemove) {
        if (Test-Path $item) {
            Remove-Item -Recurse -Force $item -ErrorAction SilentlyContinue
            Write-Host "Removed: $item" -ForegroundColor Gray
        }
    }

    # 特别删除 src 文件夹下的 egg-info
    if (Test-Path "src\jss_api_extend.egg-info") {
        Remove-Item -Recurse -Force "src\jss_api_extend.egg-info" -ErrorAction SilentlyContinue
        Write-Host "Removed: src\jss_api_extend.egg-info" -ForegroundColor Gray
    }
    
    # Remove Python cache files
    Get-ChildItem -Recurse -Name "__pycache__" -ErrorAction SilentlyContinue | ForEach-Object {
        Remove-Item -Recurse -Force $_ -ErrorAction SilentlyContinue
    }
    
    Get-ChildItem -Recurse -Name "*.pyc" -ErrorAction SilentlyContinue | ForEach-Object {
        Remove-Item -Force $_ -ErrorAction SilentlyContinue
    }
    
    Write-Host "Clean complete!" -ForegroundColor Green
}

function Invoke-Format {
    Write-Host "Formatting code..." -ForegroundColor Yellow
    
    python -m black src tests examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Black formatting failed!"
        exit 1
    }
    
    python -m isort src tests examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "isort formatting failed!"
        exit 1
    }
    
    Write-Host "Format complete!" -ForegroundColor Green
}

function Invoke-Lint {
    Write-Host "Running linting..." -ForegroundColor Yellow
    
    python -m flake8 src tests examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Flake8 linting failed!"
        exit 1
    }
    
    python -m mypy src
    if ($LASTEXITCODE -ne 0) {
        Write-Error "MyPy type checking failed!"
        exit 1
    }
    
    Write-Host "Linting complete!" -ForegroundColor Green
}

function Invoke-Test {
    Write-Host "Running tests..." -ForegroundColor Yellow
    
    python -m pytest
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Tests failed!"
        exit 1
    }
    
    Write-Host "Tests complete!" -ForegroundColor Green
}

function Invoke-Build {
    Write-Host "Building package..." -ForegroundColor Yellow
    
    Invoke-Clean
    
    python -m build
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed!"
        exit 1
    }
    
    Write-Host "Checking package..." -ForegroundColor Yellow
    python -m twine check dist\*
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Package check failed!"
        exit 1
    }
    
    Write-Host "Build complete!" -ForegroundColor Green
}

function Invoke-UploadTest {
    Write-Host "Uploading to Test PyPI..." -ForegroundColor Yellow
    
    Invoke-Build
    
    python -m twine upload --repository testpypi dist\*
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Upload to Test PyPI failed!"
        exit 1
    }
    
    Write-Host "Upload to Test PyPI complete!" -ForegroundColor Green
}

function Invoke-Upload {
    Write-Host "Uploading to PyPI..." -ForegroundColor Yellow
    
    Invoke-Build
    
    python -m twine upload dist\*
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Upload to PyPI failed!"
        exit 1
    }
    
    Write-Host "Upload to PyPI complete!" -ForegroundColor Green
}

function Invoke-Check {
    Write-Host "Running all checks..." -ForegroundColor Yellow
    
    Write-Host "Checking code format..." -ForegroundColor Yellow
    python -m black --check src tests examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Code format check failed!"
        exit 1
    }
    
    python -m isort --check-only src tests examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Import sort check failed!"
        exit 1
    }
    
    Invoke-Lint
    Invoke-Test
    
    Write-Host "All checks passed!" -ForegroundColor Green
}

function Invoke-Dev {
    Write-Host "Running development cycle..." -ForegroundColor Yellow
    
    Invoke-Format
    Invoke-Lint
    Invoke-Test
    
    Write-Host "Development cycle complete!" -ForegroundColor Green
}

# Main script logic
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "clean" { Invoke-Clean }
    "format" { Invoke-Format }
    "lint" { Invoke-Lint }
    "test" { Invoke-Test }
    "build" { Invoke-Build }
    "upload-test" { Invoke-UploadTest }
    "upload" { Invoke-Upload }
    "check" { Invoke-Check }
    "dev" { Invoke-Dev }
    default {
        Write-Error "Unknown command: $Command"
        Write-Host "Use '.\build.ps1 help' to see available commands." -ForegroundColor Yellow
        exit 1
    }
}
