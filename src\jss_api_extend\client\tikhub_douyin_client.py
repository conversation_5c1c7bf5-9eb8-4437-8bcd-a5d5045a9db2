# -*- coding: utf-8 -*-

import time
from typing import Optional

from ..config.tikhub_config import tikhub_config
from ..utils.http_utils import JSSHttpUtils


class TikhubDouyinClient:
    def __init__(self, logger_, max_retries: int = 3, retry_delay: int = 2):

        self.base_url = tikhub_config.get("base_url")
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.authorization = tikhub_config.get("api_key")
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {self.authorization}",
        }
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self.logger_ = logger_

    def __process_request(
        self, url: str, headers: dict, method: str, params: dict = None
    ) -> Optional[dict]:

        for attempt in range(self._max_retries):
            try:
                if method == "GET":
                    response = JSSHttpUtils.get(url=url, headers=headers)
                else:
                    response = JSSHttpUtils.post(url, data=params, headers=headers)

                if not response or not response.text.strip() or not response.content:
                    error_message = (
                        "第 {0} 次响应内容为空, 状态码: {1}, URL:{2}".format(
                            attempt + 1, response.status_code, response.url
                        )
                    )
                    if self.logger_:
                        self.logger_.info(error_message)

                    if attempt < self._max_retries - 1:
                        if self.logger_:
                            self.logger_.info(
                                f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试..."
                            )
                        time.sleep(self._retry_delay)
                    continue

                response.raise_for_status()
                return response.json()

            except Exception as e:
                if self.logger_:
                    self.logger_.error(
                        f"第 {attempt + 1} 次请求发生异常: {str(e)}, URL: {url}"
                    )
                if attempt < self._max_retries - 1:
                    if self.logger_:
                        self.logger_.info(
                            f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试..."
                        )
                    time.sleep(self._retry_delay)
                else:
                    return None

        if self.logger_:
            self.logger_.error(f"所有 {self._max_retries} 次重试都失败了，URL: {url}")

        return None

    def fetch_general_search_v1(
        self,
        keyword: str,
        cursor: int = 0,
        sort_type: str = "0",
        publish_time: str = "0",
        filter_duration: str = "0",
        content_type: str = "0",
        search_id: str = "",
    ) -> dict:
        """
        keyword: 搜索关键词，如 "猫咪"
        cursor: 翻页游标（首次请求传 0，翻页时使用上次响应的 cursor）
        sort_type: 排序方式
            0: 综合排序
            1: 最多点赞
            2: 最新发布
        publish_time: 发布时间筛选
            0: 不限
            1: 最近一天
            7: 最近一周
            180: 最近半年
        filter_duration: 视频时长筛选
            0: 不限
            0-1: 1 分钟以内
            1-5: 1-5 分钟
            5-10000: 5 分钟以上
        content_type: 内容类型筛选
            0: 不限
            1: 视频
            2: 图片
            3: 文章
        search_id: 搜索ID（分页时使用）
        """
        endpoint = "/api/v1/douyin/search/fetch_general_search_v1"
        body = {
            "keyword": keyword,
            "cursor": cursor,
            "sort_type": sort_type,
            "publish_time": publish_time,
            "filter_duration": filter_duration,
            "content_type": content_type,
            "search_id": search_id,
        }

        url: str = self.base_url + f"{endpoint}"

        search_result: dict = self.__process_request(
            url=url, headers=self.headers, method="POST", params=body
        )
        return search_result

    def fetch_user_post_videos(
        self, sec_user_id: str, max_cursor: int = 0, count: int = 20
    ) -> dict:
        """
        获取用户作品列表

        :param sec_user_id: 用户sec_user_id
        :param max_cursor: 最大游标，用于翻页，第一页为0，第二页为第一次响应中的max_cursor值
        :param count: 最大数量
        :return: 用户作品列表响应数据
        """
        endpoint = "/api/v1/douyin/app/v3/fetch_user_post_videos"
        params = {"sec_user_id": sec_user_id, "max_cursor": max_cursor, "count": count}

        # 构建带查询参数的URL
        query_string = "&".join([f"{key}={value}" for key, value in params.items()])
        url: str = self.base_url + f"{endpoint}?{query_string}"

        user_videos_result: dict = self.__process_request(
            url=url, headers=self.headers, method="GET"
        )
        return user_videos_result

    def fetch_user_profile(self, sec_user_id: str) -> dict:
        """
        获取指定用户的信息

        :param sec_user_id: 用户sec_user_id
        :return: 用户信息响应数据
        """
        endpoint = "/api/v1/douyin/app/v3/handler_user_profile"
        params = {"sec_user_id": sec_user_id}

        # 构建带查询参数的URL
        query_string = "&".join([f"{key}={value}" for key, value in params.items()])
        url: str = self.base_url + f"{endpoint}?{query_string}"

        user_profile_result: dict = self.__process_request(
            url=url, headers=self.headers, method="GET"
        )
        return user_profile_result
