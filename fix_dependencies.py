#!/usr/bin/env python3
"""
依赖问题修复脚本
根据检查结果修复项目中的依赖问题
"""

import os
import re
from pathlib import Path
from typing import Dict, List


class DependencyFixer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_root = self.project_root / "src"
        self.fixes_applied = []
        
    def fix_import_paths(self):
        """修复导入路径问题"""
        print("🔧 修复导入路径问题...")

        # 需要修复的导入映射 - 使用正则表达式进行更精确的匹配
        import_fixes = {
            # 错误的导入 -> 正确的导入
            'from jss_api_extend.xhs_detail_handler import': 'from jss_api_extend.adapter.xhs_detail_handler import',
            'from jss_api_extend.time_utils import': 'from jss_api_extend.utils.time_utils import',
            'from jss_api_extend.url_utils import': 'from jss_api_extend.utils.url_utils import',
            'from jss_api_extend.dingtalk_bitable_notifier import': 'from jss_api_extend.bitable.dingtalk_bitable_notifier import',
            'from jss_api_extend.file_utils import': 'from jss_api_extend.utils.file_utils import',
            'from jss_api_extend.tikhub_kuaishou_client import': 'from jss_api_extend.client.tikhub_kuaishou_client import',
            'from jss_api_extend.douyin_search_handler import': 'from jss_api_extend.adapter.douyin_search_handler import',
            'from jss_api_extend.wxvideo_handler import': 'from jss_api_extend.adapter.wxvideo_handler import',
            'from jss_api_extend.tikhub_config import': 'from jss_api_extend.config.tikhub_config import',
            'from jss_api_extend.tikhub_douyin_client import': 'from jss_api_extend.client.tikhub_douyin_client import',
            'from jss_api_extend.xhs_comment_handler import': 'from jss_api_extend.adapter.xhs_comment_handler import',
            'from jss_api_extend.http_utils import': 'from jss_api_extend.utils.http_utils import',
            'from jss_api_extend.douyin_detail_handler import': 'from jss_api_extend.adapter.douyin_detail_handler import',
            'from jss_api_extend.uuid_utils import': 'from jss_api_extend.utils.uuid_utils import',
            'from jss_api_extend.tiktok_search_handler import': 'from jss_api_extend.adapter.tiktok_search_handler import',
            'from jss_api_extend.kuaishou_detail_handler import': 'from jss_api_extend.adapter.kuaishou_detail_handler import',
            'from jss_api_extend.dingtalk_bitable_factory import': 'from jss_api_extend.bitable.dingtalk_bitable_factory import',
            'from jss_api_extend.string_utils import': 'from jss_api_extend.utils.string_utils import',
            'from jss_api_extend.region_code_converter import': 'from jss_api_extend.utils.region_code_converter import',
            'from jss_api_extend.tikhub_xhs_client import': 'from jss_api_extend.client.tikhub_xhs_client import',
            'from jss_api_extend.dingtalk_alerter import': 'from jss_api_extend.bitable.dingtalk_alerter import',
            'from jss_api_extend.dingtalk_linker import': 'from jss_api_extend.client.dingtalk_linker import',
            'from jss_api_extend.res_limit import': 'from jss_api_extend.utils.res_limit import',
            'from jss_api_extend.lark_bitable import': 'from jss_api_extend.client.lark_bitable import',
            'from jss_api_extend.json_encoder import': 'from jss_api_extend.utils.json_encoder import',
            
            # 简化的导入
            'import jss_api_extend.xhs_detail_handler': 'import jss_api_extend.adapter.xhs_detail_handler',
            'import jss_api_extend.time_utils': 'import jss_api_extend.utils.time_utils',
            'import jss_api_extend.url_utils': 'import jss_api_extend.utils.url_utils',
            'import jss_api_extend.dingtalk_bitable_notifier': 'import jss_api_extend.bitable.dingtalk_bitable_notifier',
            'import jss_api_extend.file_utils': 'import jss_api_extend.utils.file_utils',
            'import jss_api_extend.tikhub_kuaishou_client': 'import jss_api_extend.client.tikhub_kuaishou_client',
            'import jss_api_extend.douyin_search_handler': 'import jss_api_extend.adapter.douyin_search_handler',
            'import jss_api_extend.wxvideo_handler': 'import jss_api_extend.adapter.wxvideo_handler',
            'import jss_api_extend.tikhub_config': 'import jss_api_extend.config.tikhub_config',
            'import jss_api_extend.tikhub_douyin_client': 'import jss_api_extend.client.tikhub_douyin_client',
            'import jss_api_extend.xhs_comment_handler': 'import jss_api_extend.adapter.xhs_comment_handler',
            'import jss_api_extend.http_utils': 'import jss_api_extend.utils.http_utils',
            'import jss_api_extend.douyin_detail_handler': 'import jss_api_extend.adapter.douyin_detail_handler',
            'import jss_api_extend.uuid_utils': 'import jss_api_extend.utils.uuid_utils',
            'import jss_api_extend.tiktok_search_handler': 'import jss_api_extend.adapter.tiktok_search_handler',
            'import jss_api_extend.kuaishou_detail_handler': 'import jss_api_extend.adapter.kuaishou_detail_handler',
            'import jss_api_extend.dingtalk_bitable_factory': 'import jss_api_extend.bitable.dingtalk_bitable_factory',
            'import jss_api_extend.string_utils': 'import jss_api_extend.utils.string_utils',
            'import jss_api_extend.region_code_converter': 'import jss_api_extend.utils.region_code_converter',
            'import jss_api_extend.tikhub_xhs_client': 'import jss_api_extend.client.tikhub_xhs_client',
            'import jss_api_extend.dingtalk_alerter': 'import jss_api_extend.bitable.dingtalk_alerter',
            'import jss_api_extend.dingtalk_linker': 'import jss_api_extend.client.dingtalk_linker',
            'import jss_api_extend.res_limit': 'import jss_api_extend.utils.res_limit',
            'import jss_api_extend.lark_bitable': 'import jss_api_extend.client.lark_bitable',
            'import jss_api_extend.json_encoder': 'import jss_api_extend.utils.json_encoder',
        }
        
        # 遍历所有Python文件
        for py_file in self.find_python_files():
            self.fix_file_imports(py_file, import_fixes)
    
    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []
        for root, dirs, files in os.walk(self.src_root):
            dirs[:] = [d for d in dirs if d != '__pycache__']
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        return python_files
    
    def fix_file_imports(self, file_path: Path, import_fixes: Dict[str, str]):
        """修复单个文件的导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用修复
            for wrong_import, correct_import in import_fixes.items():
                if wrong_import in content:
                    content = content.replace(wrong_import, correct_import)
                    self.fixes_applied.append(f"修复 {file_path.relative_to(self.project_root)}: {wrong_import} -> {correct_import}")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 修复文件: {file_path.relative_to(self.project_root)}")
                
        except Exception as e:
            print(f"❌ 修复文件失败 {file_path}: {e}")
    
    def fix_relative_imports(self):
        """修复相对导入问题"""
        print("🔧 修复相对导入问题...")
        
        # 特定的相对导入修复
        relative_fixes = {
            'from adapter.models.wxvideo_models import': 'from jss_api_extend.adapter.models.wxvideo_models import',
            'from adapter import': 'from jss_api_extend.adapter import',
            'from client import': 'from jss_api_extend.client import',
            'from bitable import': 'from jss_api_extend.bitable import',
        }
        
        for py_file in self.find_python_files():
            self.fix_file_imports(py_file, relative_fixes)
    
    def check_missing_external_deps(self):
        """检查缺失的外部依赖"""
        print("📦 检查外部依赖...")
        
        missing_deps = [
            'importlib_metadata',
            'dingtalk_alerter', 
            'resource'
        ]
        
        suggestions = {
            'importlib_metadata': 'pip install importlib-metadata',
            'dingtalk_alerter': '这可能是自定义模块，请检查是否需要实现',
            'resource': '这可能是 Unix 系统的 resource 模块，在 Windows 上不可用'
        }
        
        print("\n🔍 外部依赖建议:")
        for dep in missing_deps:
            suggestion = suggestions.get(dep, f'pip install {dep}')
            print(f"  - {dep}: {suggestion}")
    
    def generate_requirements_txt(self):
        """生成 requirements.txt 文件"""
        print("📝 生成 requirements.txt...")
        
        # 基于检查结果的已知依赖
        known_deps = [
            'requests',
            'numpy', 
            'lark-oapi',
            'tikhub',
            'alibabacloud-dingtalk',
            'alibabacloud-tea-openapi',
            'alibabacloud-tea-util',
            'python-dateutil',
            'importlib-metadata',
        ]
        
        requirements_file = self.project_root / "requirements.txt"
        
        # 检查是否已存在
        if requirements_file.exists():
            print(f"⚠️  requirements.txt 已存在: {requirements_file}")
            return
        
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write("# 项目依赖\n")
            f.write("# 自动生成，请根据实际需要调整版本号\n\n")
            for dep in sorted(known_deps):
                f.write(f"{dep}\n")
        
        print(f"✅ 生成 requirements.txt: {requirements_file}")
    
    def run_fixes(self):
        """运行所有修复"""
        print("🚀 开始修复依赖问题...")
        
        # 修复导入路径
        self.fix_import_paths()
        
        # 修复相对导入
        self.fix_relative_imports()
        
        # 检查外部依赖
        self.check_missing_external_deps()
        
        # 生成 requirements.txt
        self.generate_requirements_txt()
        
        # 总结
        print(f"\n📊 修复总结:")
        print(f"  应用的修复数量: {len(self.fixes_applied)}")
        
        if self.fixes_applied:
            print(f"\n🔧 应用的修复:")
            for fix in self.fixes_applied[:10]:  # 只显示前10个
                print(f"  - {fix}")
            if len(self.fixes_applied) > 10:
                print(f"  ... 还有 {len(self.fixes_applied) - 10} 个修复")
        
        print(f"\n✅ 依赖修复完成!")
        print(f"💡 建议重新运行依赖检查脚本验证修复结果")


def main():
    """主函数"""
    project_root = os.getcwd()
    
    print(f"🔧 项目依赖修复工具")
    print(f"📂 项目根目录: {project_root}")
    
    fixer = DependencyFixer(project_root)
    fixer.run_fixes()


if __name__ == "__main__":
    main()
