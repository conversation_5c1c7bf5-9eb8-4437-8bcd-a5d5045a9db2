.PHONY: help install install-dev test test-cov lint format clean build upload upload-test docs

# Default target
help:
	@echo "Available targets:"
	@echo "  install      Install the package"
	@echo "  install-dev  Install the package in development mode with dev dependencies"
	@echo "  test         Run tests"
	@echo "  test-cov     Run tests with coverage"
	@echo "  lint         Run linting (flake8, mypy)"
	@echo "  format       Format code (black, isort)"
	@echo "  clean        Clean build artifacts"
	@echo "  build        Build the package"
	@echo "  upload       Upload to PyPI"
	@echo "  upload-test  Upload to Test PyPI"
	@echo "  docs         Generate documentation"

# Installation
install:
	pip install .

install-dev:
	pip install -e ".[dev,test]"

# Testing
test:
	pytest

test-cov:
	pytest --cov=jss_api_extend --cov-report=html --cov-report=term

test-integration:
	pytest -m integration

test-unit:
	pytest -m "not integration"

# Code quality
lint:
	flake8 src tests examples
	mypy src

format:
	black src tests examples
	isort src tests examples

format-check:
	black --check src tests examples
	isort --check-only src tests examples

# Build and distribution
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf src/jss_api_extend.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

upload: build
	python -m twine upload dist/*

upload-test: build
	python -m twine upload --repository testpypi dist/*

# Development
dev-setup: install-dev
	pre-commit install

# Documentation (if using sphinx or similar)
docs:
	@echo "Documentation generation not configured yet"

# Check everything before commit
check: format-check lint test
	@echo "All checks passed!"

# Quick development cycle
dev: format lint test
	@echo "Development cycle complete!"
