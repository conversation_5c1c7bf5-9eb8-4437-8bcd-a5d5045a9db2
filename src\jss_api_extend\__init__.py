# -*- coding: utf-8 -*-
"""
JSS API Extension Package

A comprehensive Python package for social media platform APIs (Douyin, Xiaohongshu, Kuaishou) 
and DingTalk Bitable integration.

This package provides:
- adapter: API adapters for social media platforms (Douyin, Xiaohongshu, Kuaishou)
- bitable: DingTalk Bitable integration and notification services
- client: Internal API clients for third-party services
- common: Common utilities and configurations
- utils: Utility functions and helpers
"""
from .adapter.models.wxvideo_models import WxVideoModel

# 版本信息从 pyproject.toml 中动态获取
try:
    from importlib.metadata import version, PackageNotFoundError
except ImportError:
    # Python < 3.8
    from importlib_metadata import version, PackageNotFoundError  # type: ignore

try:
    __version__ = version("jss-api-extend")
except PackageNotFoundError:
    # 开发模式下的后备版本
    __version__ = "0.1.7-dev"
__author__ = "JSS Team"
__email__ = "<EMAIL>"

# Import main classes for easy access
from .adapter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    KuaiShouDetailHandler,
    XhsDetailHandler,
    XhsCommentHandler,
    TiktokSearchHandler,
    WxVideoHandler,
)

from .bitable import (
    DingTalkBitableNotifier,
    DingtalkBitableFactory,
    DingTalkAlerter,
)

from .client import (
    TikhubDouyinClient,
    TikhubXhsClient,
    TikhubKuaiShouClient,
    LarkBitable,
    ServiceClient,
    DingTalkLinker,
)

# Export all main classes
__all__ = [
    # Adapter classes
    "DouyinDetailHandler",
    "DouyinSearchHandler",
    "DouyinUserHandler",
    "KuaiShouDetailHandler",
    "XhsDetailHandler",
    "XhsCommentHandler",
    "TiktokSearchHandler",
    "WxVideoHandler",
    
    # Bitable classes
    "DingTalkBitableNotifier",
    "DingtalkBitableFactory",
    "DingTalkAlerter",

    "ServiceClient",
]
