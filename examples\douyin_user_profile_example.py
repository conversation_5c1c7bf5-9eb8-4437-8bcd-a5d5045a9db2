# -*- coding: utf-8 -*-
"""
抖音用户主页数据获取示例

本示例演示如何使用 jss_api_extend 包获取抖音用户主页数据。
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend.adapter.douyin_user_handler import DouyinUserHandler
from env import Env


def douyin_user_profile_example():
    """抖音用户主页数据获取示例"""
    print("=== 抖音用户主页数据获取示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建抖音用户处理器
    user_handler = DouyinUserHandler(logger)
    
    # 示例用户 sec_uid (这是一个示例ID，实际使用时需要替换为真实的用户ID)
    sec_uid = "MS4wLjABAAAAW9FWcqS7RdQAWPd2AA5fL_ilmqsIFUCQ_Iym6Yh9_cUa6ZRqVLjVQSUjlHrfXY1Y"
    
    try:
        # 获取用户主页数据
        print(f"正在获取用户 {sec_uid} 的主页数据...")
        user_profile = user_handler.get_user_profile(sec_uid)
        
        if user_profile:
            print("✅ 成功获取用户主页数据:")
            print(f"  用户昵称: {user_profile.nickname}")
            print(f"  用户签名: {user_profile.signature}")
            print(f"  唯一ID: {user_profile.sec_uid}")
            print(f"  主页链接: {user_profile.home_url}")
            print(f"  作品数量: {user_profile.work_count}")
            print(f"  粉丝数量: {user_profile.follower_count}")
            print(f"  关注数量: {user_profile.following_count}")
            print(f"  喜欢数量: {user_profile.favoriting_count}")
            print(f"  总点赞数: {user_profile.total_favorited}")
            print(f"  IP地址: {user_profile.ip_location}")
            
            # 性别显示
            gender_map = {0: "女", 1: "男", -1: "未知"}
            gender_text = gender_map.get(user_profile.gender, "未知")
            print(f"  性别: {gender_text}")
            
        else:
            print("❌ 未能获取用户主页数据")
            
    except Exception as e:
        print(f"❌ 获取用户主页数据时发生错误: {e}")


def main():
    """主函数"""
    print("🚀 JSS API Extension - 抖音用户主页数据获取演示")
    print("=" * 60)
    
    # 运行用户主页数据获取示例
    douyin_user_profile_example()
    
    print("\n✨ 演示完成!")


if __name__ == "__main__":
    # 运行主函数
    main()
