#!/usr/bin/env python3
"""
精确的导入修复脚本
"""

import os
import re
from pathlib import Path


def fix_specific_imports():
    """修复具体的导入问题"""
    project_root = Path(os.getcwd())
    src_root = project_root / "src"
    
    fixes_applied = []
    
    # 修复 __init__.py 中的 importlib_metadata 问题
    init_file = src_root / "jss_api_extend" / "__init__.py"
    if init_file.exists():
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复 importlib_metadata 导入
        if 'from importlib_metadata import' in content:
            content = content.replace(
                'from importlib_metadata import version, PackageNotFoundError',
                'from importlib_metadata import version, PackageNotFoundError  # type: ignore'
            )
            
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(content)
            fixes_applied.append(f"修复 {init_file.relative_to(project_root)}: importlib_metadata 导入")
    
    # 查找并修复所有错误的导入
    for py_file in find_python_files(src_root):
        content_changed = False
        
        with open(py_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复各种错误的导入模式
        import_patterns = [
            # (错误模式, 正确模式)
            (r'from jss_api_extend\.([^.]+) import', r'from jss_api_extend.\1.\1 import'),
            (r'import jss_api_extend\.([^.]+)$', r'import jss_api_extend.\1.\1'),
        ]
        
        # 特定的模块映射
        module_mappings = {
            'xhs_detail_handler': 'adapter.xhs_detail_handler',
            'time_utils': 'utils.time_utils',
            'url_utils': 'utils.url_utils',
            'dingtalk_bitable_notifier': 'bitable.dingtalk_bitable_notifier',
            'file_utils': 'utils.file_utils',
            'tikhub_kuaishou_client': 'client.tikhub_kuaishou_client',
            'douyin_search_handler': 'adapter.douyin_search_handler',
            'wxvideo_handler': 'adapter.wxvideo_handler',
            'tikhub_config': 'config.tikhub_config',
            'tikhub_douyin_client': 'client.tikhub_douyin_client',
            'xhs_comment_handler': 'adapter.xhs_comment_handler',
            'http_utils': 'utils.http_utils',
            'douyin_detail_handler': 'adapter.douyin_detail_handler',
            'uuid_utils': 'utils.uuid_utils',
            'tiktok_search_handler': 'adapter.tiktok_search_handler',
            'kuaishou_detail_handler': 'adapter.kuaishou_detail_handler',
            'dingtalk_bitable_factory': 'bitable.dingtalk_bitable_factory',
            'string_utils': 'utils.string_utils',
            'region_code_converter': 'utils.region_code_converter',
            'tikhub_xhs_client': 'client.tikhub_xhs_client',
            'dingtalk_alerter': 'bitable.dingtalk_alerter',
            'dingtalk_linker': 'client.dingtalk_linker',
            'res_limit': 'utils.res_limit',
            'lark_bitable': 'client.lark_bitable',
            'json_encoder': 'utils.json_encoder',
        }
        
        # 应用模块映射修复
        for wrong_module, correct_path in module_mappings.items():
            # 修复 from 导入
            wrong_from = f'from jss_api_extend.{wrong_module} import'
            correct_from = f'from jss_api_extend.{correct_path} import'
            if wrong_from in content:
                content = content.replace(wrong_from, correct_from)
                content_changed = True
            
            # 修复 import 导入
            wrong_import = f'import jss_api_extend.{wrong_module}'
            correct_import = f'import jss_api_extend.{correct_path}'
            if wrong_import in content:
                content = content.replace(wrong_import, correct_import)
                content_changed = True
        
        # 修复相对导入问题
        relative_fixes = [
            ('from adapter.models.wxvideo_models import', 'from jss_api_extend.adapter.models.wxvideo_models import'),
            ('from adapter import', 'from jss_api_extend.adapter import'),
            ('from client import', 'from jss_api_extend.client import'),
            ('from bitable import', 'from jss_api_extend.bitable import'),
        ]
        
        for wrong, correct in relative_fixes:
            if wrong in content:
                content = content.replace(wrong, correct)
                content_changed = True
        
        # 如果内容有变化，写回文件
        if content_changed and content != original_content:
            with open(py_file, 'w', encoding='utf-8') as f:
                f.write(content)
            fixes_applied.append(f"修复 {py_file.relative_to(project_root)}: 导入路径")
    
    return fixes_applied


def find_python_files(src_root):
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(src_root):
        dirs[:] = [d for d in dirs if d != '__pycache__']
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    return python_files


def create_missing_init_files():
    """创建缺失的 __init__.py 文件"""
    project_root = Path(os.getcwd())
    src_root = project_root / "src"
    
    # 需要 __init__.py 的目录
    directories_needing_init = [
        src_root / "jss_api_extend",
        src_root / "jss_api_extend" / "adapter",
        src_root / "jss_api_extend" / "adapter" / "models",
        src_root / "jss_api_extend" / "bitable",
        src_root / "jss_api_extend" / "client",
        src_root / "jss_api_extend" / "client" / "model",
        src_root / "jss_api_extend" / "client" / "qb",
        src_root / "jss_api_extend" / "config",
        src_root / "jss_api_extend" / "utils",
    ]
    
    created_files = []
    
    for directory in directories_needing_init:
        if directory.exists():
            init_file = directory / "__init__.py"
            if not init_file.exists():
                init_file.touch()
                created_files.append(str(init_file.relative_to(project_root)))
    
    return created_files


def main():
    """主函数"""
    print("🔧 精确导入修复工具")
    
    # 创建缺失的 __init__.py 文件
    print("📁 创建缺失的 __init__.py 文件...")
    created_files = create_missing_init_files()
    if created_files:
        print(f"✅ 创建了 {len(created_files)} 个 __init__.py 文件")
        for file in created_files:
            print(f"  - {file}")
    else:
        print("ℹ️  所有必要的 __init__.py 文件都已存在")
    
    # 修复导入问题
    print("\n🔧 修复导入问题...")
    fixes_applied = fix_specific_imports()
    
    if fixes_applied:
        print(f"✅ 应用了 {len(fixes_applied)} 个修复")
        for fix in fixes_applied[:10]:  # 只显示前10个
            print(f"  - {fix}")
        if len(fixes_applied) > 10:
            print(f"  ... 还有 {len(fixes_applied) - 10} 个修复")
    else:
        print("ℹ️  没有发现需要修复的导入问题")
    
    print("\n✅ 修复完成!")
    print("💡 建议重新运行依赖检查脚本验证修复结果")


if __name__ == "__main__":
    main()
