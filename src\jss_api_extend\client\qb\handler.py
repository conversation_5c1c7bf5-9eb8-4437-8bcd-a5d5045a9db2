# -*- coding: utf-8 -*-

from ..client.qb.api_client import ApiClient
from client.qb.models import ApiConfig, Platform
from utils.time_utils import TimeUtils


class Handler:
    def __init__(self, logger_, platform: Platform):
        self.logger_ = logger_
        self._platform = platform

        # Initialize configuration
        config = ApiConfig(
            project_id=10312,
            sign="xx"
        )

        # Create API client
        self.client = ApiClient(config, logger_)

    def sync_data(self):
        pass

    def query_author_new_article_list(self, author_code, day=None) -> list:
        if day is None:
            day = TimeUtils.get_yst_datetime()

        if self._platform == "dy":
            return self.client.get_one_douyin_articles(day, author_code)
        elif self._platform == "xhs":
            return self.client.get_one_xhs_articles(day, author_code)
        elif self._platform == "wxvideo":
            return self.client.get_one_wxvideo_articles(day, author_code)

        return []

    def query_article_detail(self, key):
        try:
            return self.client.get_article_detail(self._platform, key)
        except Exception as e:
            self.logger_.error("query_article_detail error, %s", e)
            return None

    def query_article_detail_adapter(self, platform: str, work_url: str):
        """
        多平台的适配
        @param platform:
        @param work_id:
        @return:
        """
        try:
            self.logger_.info("query_article_detail_adapter, platform: %s, work_url: %s", platform, work_url)
            return self.client.get_article_detail(Platform(platform), work_url)
        except Exception as e:
            self.logger_.error("query_article_detail error, %s", e)
            return None

    def query_article_comment(self, key, count: int) -> list:
        return self.client.get_article_comments(self._platform, key, count)

    def query_article_sub_comment(self, key) -> list:
        return self.client.get_article_sub_comments(self._platform, key)
