============================================================
项目依赖检查报告
============================================================

📊 基本统计:
  Python文件数量: 46
  内部模块数量: 54
  外部依赖数量: 31

📦 外部依赖:
  - alibabacloud_dingtalk.notable_1_0
  - alibabacloud_dingtalk.notable_1_0.client
  - alibabacloud_dingtalk.notable_1_0.models
  - alibabacloud_dingtalk.oauth2_1_0
  - alibabacloud_dingtalk.oauth2_1_0.client
  - alibabacloud_tea_openapi
  - alibabacloud_tea_util
  - base64
  - dataclasses
  - datetime
  - dateutil
  - enum
  - hashlib
  - hmac
  - importlib.metadata
  - importlib_metadata
  - json
  - lark_oapi
  - lark_oapi.api.bitable.v1
  - numpy
  - os
  - re
  - requests
  - resource
  - shutil
  - sys
  - tikhub
  - time
  - typing
  - urllib.parse
  - uuid

❌ 发现的问题 (31 个):
  1. 外部模块不可用: D:\code\py\jss-api-extend\src\jss_api_extend\__init__.py:22 - importlib_metadata
  2. 外部模块不可用: D:\code\py\jss-api-extend\src\jss_api_extend\utils\res_limit.py:13 - resource
  3. 模块文件不存在: jss_api_extend.lark_bitable (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\lark_bitable.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\lark_bitable\__init__.py)
  4. 模块文件不存在: client (期望路径: D:\code\py\jss-api-extend\src\client.py 或 D:\code\py\jss-api-extend\src\client\__init__.py)
  5. 模块文件不存在: jss_api_extend.dingtalk_bitable_notifier (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_bitable_notifier.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_bitable_notifier\__init__.py)
  6. 模块文件不存在: jss_api_extend.url_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\url_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\url_utils\__init__.py)
  7. 模块文件不存在: jss_api_extend.tikhub_config (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_config.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_config\__init__.py)
  8. 模块文件不存在: jss_api_extend.res_limit (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\res_limit.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\res_limit\__init__.py)
  9. 模块文件不存在: jss_api_extend.file_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\file_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\file_utils\__init__.py)
  10. 模块文件不存在: jss_api_extend.string_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\string_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\string_utils\__init__.py)
  11. 模块文件不存在: jss_api_extend.json_encoder (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\json_encoder.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\json_encoder\__init__.py)
  12. 模块文件不存在: jss_api_extend.xhs_comment_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\xhs_comment_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\xhs_comment_handler\__init__.py)
  13. 模块文件不存在: jss_api_extend.kuaishou_detail_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\kuaishou_detail_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\kuaishou_detail_handler\__init__.py)
  14. 模块文件不存在: bitable (期望路径: D:\code\py\jss-api-extend\src\bitable.py 或 D:\code\py\jss-api-extend\src\bitable\__init__.py)
  15. 模块文件不存在: jss_api_extend.tiktok_search_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\tiktok_search_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\tiktok_search_handler\__init__.py)
  16. 模块文件不存在: jss_api_extend.uuid_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\uuid_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\uuid_utils\__init__.py)
  17. 模块文件不存在: adapter (期望路径: D:\code\py\jss-api-extend\src\adapter.py 或 D:\code\py\jss-api-extend\src\adapter\__init__.py)
  18. 模块文件不存在: jss_api_extend.tikhub_kuaishou_client (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_kuaishou_client.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_kuaishou_client\__init__.py)
  19. 模块文件不存在: jss_api_extend.time_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\time_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\time_utils\__init__.py)
  20. 模块文件不存在: jss_api_extend.region_code_converter (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\region_code_converter.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\region_code_converter\__init__.py)
  21. 模块文件不存在: jss_api_extend.dingtalk_linker (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_linker.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_linker\__init__.py)
  22. 模块文件不存在: adapter.models.wxvideo_models (期望路径: D:\code\py\jss-api-extend\src\adapter\models\wxvideo_models.py 或 D:\code\py\jss-api-extend\src\adapter\models\wxvideo_models\__init__.py)
  23. 模块文件不存在: jss_api_extend.dingtalk_alerter (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_alerter.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_alerter\__init__.py)
  24. 模块文件不存在: jss_api_extend.douyin_search_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\douyin_search_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\douyin_search_handler\__init__.py)
  25. 模块文件不存在: jss_api_extend.tikhub_xhs_client (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_xhs_client.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_xhs_client\__init__.py)
  26. 模块文件不存在: jss_api_extend.wxvideo_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\wxvideo_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\wxvideo_handler\__init__.py)
  27. 模块文件不存在: jss_api_extend.xhs_detail_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\xhs_detail_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\xhs_detail_handler\__init__.py)
  28. 模块文件不存在: jss_api_extend.tikhub_douyin_client (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_douyin_client.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\tikhub_douyin_client\__init__.py)
  29. 模块文件不存在: jss_api_extend.douyin_detail_handler (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\douyin_detail_handler.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\douyin_detail_handler\__init__.py)
  30. 模块文件不存在: jss_api_extend.dingtalk_bitable_factory (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_bitable_factory.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\dingtalk_bitable_factory\__init__.py)
  31. 模块文件不存在: jss_api_extend.http_utils (期望路径: D:\code\py\jss-api-extend\src\jss_api_extend\http_utils.py 或 D:\code\py\jss-api-extend\src\jss_api_extend\http_utils\__init__.py)

🔗 模块依赖关系:
  jss_api_extend:
    └── adapter
    └── adapter.models.wxvideo_models
    └── bitable
    └── client
  jss_api_extend.adapter:
    └── jss_api_extend.douyin_detail_handler
    └── jss_api_extend.douyin_search_handler
    └── jss_api_extend.kuaishou_detail_handler
    └── jss_api_extend.tiktok_search_handler
    └── jss_api_extend.wxvideo_handler
    └── jss_api_extend.xhs_comment_handler
    └── jss_api_extend.xhs_detail_handler
  jss_api_extend.adapter.douyin_detail_handler:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.qb.handler
    └── jss_api_extend.client.qb.models
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.region_code_converter
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
  jss_api_extend.adapter.douyin_search_handler:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.tikhub_douyin_client
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.region_code_converter
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
  jss_api_extend.adapter.douyin_user_handler:
    └── jss_api_extend
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.config
    └── jss_api_extend.utils
  jss_api_extend.adapter.kuaishou_detail_handler:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.tikhub_kuaishou_client
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.region_code_converter
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
    └── jss_api_extend.utils.url_utils
  jss_api_extend.adapter.models.work_detail_model:
    └── jss_api_extend.utils
  jss_api_extend.adapter.tiktok_search_handler:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.tikhub_douyin_client
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.region_code_converter
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
  jss_api_extend.adapter.wxvideo_handler:
    └── jss_api_extend.adapter.models.wxvideo_models
    └── jss_api_extend.client.jzl_wxvideo_cllient
  jss_api_extend.adapter.xhs_comment_handler:
    └── jss_api_extend.adapter.models.comment_model
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.qb.handler
    └── jss_api_extend.client.qb.models
    └── jss_api_extend.client.tikhub_xhs_client
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
    └── jss_api_extend.utils.uuid_utils
  jss_api_extend.adapter.xhs_detail_handler:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.client.qb.handler
    └── jss_api_extend.client.qb.models
    └── jss_api_extend.client.tikhub_xhs_client
    └── jss_api_extend.utils.string_utils
    └── jss_api_extend.utils.time_utils
  jss_api_extend.bitable:
    └── jss_api_extend.dingtalk_alerter
    └── jss_api_extend.dingtalk_bitable_factory
    └── jss_api_extend.dingtalk_bitable_notifier
  jss_api_extend.bitable.base_dingtalk_bitable_notifier:
    └── jss_api_extend.bitable.dingtalk_alerter
    └── jss_api_extend.utils.http_utils
  jss_api_extend.bitable.dingtalk_alerter:
    └── jss_api_extend.utils.http_utils
  jss_api_extend.bitable.dingtalk_bitable_factory:
    └── jss_api_extend.bitable.dingtalk_bitable_notifier
  jss_api_extend.bitable.dingtalk_bitable_notifier:
    └── jss_api_extend.bitable.dingtalk_alerter
    └── jss_api_extend.client.model.dingtalk_bitable_record_info
    └── jss_api_extend.utils.http_utils
    └── jss_api_extend.utils.time_utils
  jss_api_extend.client:
    └── jss_api_extend.dingtalk_linker
    └── jss_api_extend.lark_bitable
    └── jss_api_extend.service_client
    └── jss_api_extend.tikhub_douyin_client
    └── jss_api_extend.tikhub_kuaishou_client
    └── jss_api_extend.tikhub_xhs_client
  jss_api_extend.client.lark_bitable:
    └── jss_api_extend.adapter.models.work_detail_model
    └── jss_api_extend.utils.time_utils
  jss_api_extend.client.qb.api_client:
    └── jss_api_extend.client.qb.models
    └── jss_api_extend.utils.string_utils
  jss_api_extend.client.qb.handler:
    └── jss_api_extend.client.qb.api_client
    └── jss_api_extend.client.qb.models
    └── jss_api_extend.utils.time_utils
  jss_api_extend.client.tikhub_douyin_client:
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.http_utils
  jss_api_extend.client.tikhub_kuaishou_client:
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.http_utils
  jss_api_extend.client.tikhub_xhs_client:
    └── jss_api_extend.config.tikhub_config
    └── jss_api_extend.utils.http_utils
  jss_api_extend.config:
    └── jss_api_extend.tikhub_config
  jss_api_extend.service_client:
    └── jss_api_extend.utils.http_utils
  jss_api_extend.utils:
    └── jss_api_extend.file_utils
    └── jss_api_extend.http_utils
    └── jss_api_extend.json_encoder
    └── jss_api_extend.region_code_converter
    └── jss_api_extend.res_limit
    └── jss_api_extend.string_utils
    └── jss_api_extend.time_utils
    └── jss_api_extend.url_utils
    └── jss_api_extend.uuid_utils
  jss_api_extend.utils.string_utils:
    └── jss_api_extend.utils.json_encoder