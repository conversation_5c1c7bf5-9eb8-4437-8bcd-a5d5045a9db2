# -*- coding: utf-8 -*-
"""
抖音评论获取示例

本示例演示如何使用 jss_api_extend 包获取抖音作品评论。
"""

import os
import sys

from jss_api_extend.adapter.wxvideo_comment_handler import WxVideoCommentHandler

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend.adapter.douyin_comment_handler import DouyinCommentHandler
from jss_api_extend.adapter.wxvideo_handler import WxVideoHandler
from env import Env


def douyin_comment_example():
    """抖音评论获取示例"""
    print("=== 抖音评论获取示例 ===")


    
    # 初始化日志
    logger = Env().get_main_logger()
    wx_handler = WxVideoHandler(logger)

    lis = wx_handler.query_work_tracing("v2_060000231003b20faec8c6eb891dc1d4c702ea31b077d2a024f8926fc5035d827479680aaac0@finder", 1753375161)

    logger.info(f"{lis}")
    # 创建抖音评论处理器
    comment_handler = DouyinCommentHandler(logger)
    
    # 示例作品ID (这是一个示例ID，实际使用时需要替换为真实的作品ID)
    work_id = "7530116195350711609"
    
    try:
        # 获取评论，最多获取50条
        print(f"正在获取作品 {work_id} 的评论...")
        success, all_comments, comment_list = comment_handler.query_article_comment(
            work_id=work_id, 
            count=20
        )
        
        if success and all_comments:
            print("✅ 成功获取评论:")
            print(f"  总评论数: {len(comment_list)}")
            print(f"  一级评论数: {len(all_comments)}")
            
            # 显示前几条评论
            print("\n📝 评论内容预览:")
            for i, comment in enumerate(comment_list[:5]):  # 只显示前5条
                print(f"  {i+1}. 用户: {comment.commenter_name}")
                print(f"     内容: {comment.content}")
                print(f"     点赞数: {comment.like_count}")
                print(f"     发布时间: {comment.publish_time}")
                print(f"     IP地址: {comment.location_ip}")
                
                # 如果有子评论，显示数量
                if hasattr(comment, 'sub_comments') and comment.sub_comments:
                    print(f"     子评论数: {len(comment.sub_comments)}")
                
                print()
            
            # 统计信息
            total_likes = sum(comment.like_count for comment in comment_list)
            print(f"📊 统计信息:")
            print(f"  总点赞数: {total_likes}")
            
            # 统计有子评论的数量
            comments_with_sub = sum(1 for comment in all_comments if comment.has_sub == 1)
            print(f"  有子评论的一级评论数: {comments_with_sub}")
            
        else:
            print("❌ 未能获取评论数据")
            
    except Exception as e:
        print(f"❌ 获取评论时发生错误: {e}")

def wxvideo_comment():
    """微信视频评论获取示例"""
    print("=== 微信视频评论获取示例 ===")

    # 初始化日志
    logger = Env().get_main_logger()

    # 创建微信视频评论处理器
    comment_handler = WxVideoCommentHandler(logger)

    # 示例视频ID
    object_id = "7533204068047850804"

    try:
        print(f"正在获取视频ID {object_id} 的评论...")

        # 获取评论 (获取前50条)
        success, all_comments, top_comments = comment_handler.query_video_comments(
            object_id, count=50
        )

        if success:
            print("✅ 成功获取评论:")
            print(f"  总评论数: {len(all_comments) if all_comments else 0}")
            print(f"  一级评论数: {len(top_comments) if top_comments else 0}")

            # 显示前几条评论
            if all_comments:
                print("\n  前5条评论:")
                for i, comment in enumerate(all_comments[:5]):
                    print(f"    {i+1}. {comment.commenter_name}: {comment.content}")
                    print(f"       点赞数: {comment.like_count}, 时间: {comment.publish_time}")

            if top_comments:
                print("\n  热门评论:")
                for i, comment in enumerate(top_comments[:3]):
                    print(f"    {i+1}. {comment.commenter_name}: {comment.content}")
                    print(f"       点赞数: {comment.like_count}, 时间: {comment.publish_time}")

        else:
            print("❌ 未能获取评论数据")

    except Exception as e:
        print(f"❌ 获取评论时发生错误: {e}")
        print(f"错误详情: {e}")
        print(f"错误类型: {type(e)}")


def main():
    """主函数"""
    print("🚀 JSS API Extension - 抖音评论获取演示")
    print("=" * 50)
    
    # 运行评论获取示例
    # douyin_comment_example()

    wxvideo_comment()


    
    print("\n✨ 演示完成!")


if __name__ == "__main__":

    # 运行主函数
    main()
