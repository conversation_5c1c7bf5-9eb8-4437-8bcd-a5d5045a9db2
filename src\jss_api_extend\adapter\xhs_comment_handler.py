# -*- coding: utf-8 -*-

from typing import Optional

from dateutil import parser

from ..client.qb.handler import Handler
from ..client.qb.models import Platform, Comment
from ..client.tikhub_xhs_client import TikhubXhsClient
from .models.work_detail_model import WorkDetailModel
from .models.comment_model import CommentModel
from ..utils.string_utils import StringUtils
from ..utils.time_utils import TimeUtils
from ..utils.uuid_utils import UUIDUtils


class XhsCommentHandler:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.tikhub_xhs_client = TikhubXhsClient(logger_=logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    def query_article_comment(self, note_id, count, comment_count=0) -> tuple[bool, None, None] | tuple[
        bool, list[CommentModel], list[CommentModel]]:
        try:
            all_comment_object = []
            comment_list = []

            # 获取所有一级评论
            work_url = "https://www.xiaohongshu.com/discovery/item/" + note_id

            first_level_comment_list = self._query_one_level_comments(work_url=work_url, count=count)
            if first_level_comment_list is None:
                return False, None, None

            all_comment_object.extend(first_level_comment_list)
            comment_list.extend(first_level_comment_list)
            if len(comment_list) >= count:
                return True, all_comment_object, comment_list

            for first_item in all_comment_object:
                if first_item.has_sub == 1:
                    sub_comments = self._query_sub_level_comments(first_item.work_url)
                    first_item.sub_comments.extend(sub_comments)
                    comment_list.extend(sub_comments)

                    if len(comment_list) >= count:
                        return True, all_comment_object, comment_list

            return True, all_comment_object, comment_list
        except Exception as e:
            self.logger_.error(e)
            return False, None, None

    def _query_one_level_comments(self, work_url, count, platform="xhs"):
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_comment(key=work_url, count=count)
            for comment in comment_list:
                work_comment_list.append(self._convert_qingbo_to_model(platform, comment))

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _query_sub_level_comments(self, work_url, platform="xhs") -> list | None:
        try:
            work_comment_list = []
            comment_list = self.qingbo_handler.query_article_sub_comment(key=work_url)
            for comment in comment_list:
                work_comment_list.append(self._convert_qingbo_to_model(platform, comment))

            return work_comment_list
        except Exception as e:
            self.logger_.error(e)
            return None

    def _convert_qingbo_to_model(self, platform, item: Comment):
        try:
            commenter_id = item.comment_user_id
            comment_posttime = item.comment_posttime
            comment_content = item.comment_content

            # 安全处理数字字段，确保是整数类型
            def safe_int(value, default=0):
                if isinstance(value, str):
                    try:
                        return int(value) if value.strip() else default
                    except (ValueError, AttributeError):
                        return default
                return value if isinstance(value, int) else default

            like_count = safe_int(item.comment_like_count, 0)
            has_sub = safe_int(item.has_sub, 0)
            is_sub = safe_int(item.is_sub, 0)

            comment_uuid = UUIDUtils.generate_comment_uuid(commenter_id, comment_content, comment_posttime)
            return CommentModel(
                work_url=item.news_url,
                platform=platform,
                comment_uuid=comment_uuid,
                commenter_name=item.comment_user,
                commenter_id=commenter_id,
                commenter_url="https://www.xiaohongshu.com/user/profile/" + commenter_id,
                content=comment_content,
                publish_time=comment_posttime,
                like_count=like_count,
                location_ip=item.comment_ip_location,
                first_cid=item.first_cid,
                has_sub=has_sub,
                is_sub=is_sub,
                sec_uid=item.sec_uid,
                news_parent_id=item.news_parent_id
            )
        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(item)}, {e}")

    def is_success(self, data):
        if data is None:
            return False

        if "data" not in data:
            return False

        return True

    def _generate_tiktok_article(self, data):
        try:
            code = data.get("code")
            if code != 0:
                return None

            d = data.get("data")
            if len(d) == 0:
                return None

            notes = d[0]
            user = notes.get("user")
            author_name = user.get('name', '')
            author_id = user.get('userid', '')
            author_avatar = user.get('image', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            note_list = notes.get("note_list")
            if len(note_list) == 0:
                return None

            note = note_list[0]
            work_id = note.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title', '')
            content = note.get('desc', '')
            collect_count = note.get('collected_count', '')
            comment_count = note.get('comments_count', '')
            share_count = note.get('shared_count', '')
            like_count = note.get('liked_count', '')
            publish_time = TimeUtils.ts_to_str(note.get('time'))

            ip_location = note.get('ip_location', '')
            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")

            thumbnail_link = ''
            try:
                topics = note.get('topics')
                topic = topics[0]
                thumbnail_link = topic.get("image", "")
            except Exception as e:
                pass

            return WorkDetailModel(_id=0, platform='xhs', author_id=author_id,
                                   author_identity=author_id, author_avatar=author_avatar,
                                   author_name=author_name, author_url=author_url,
                                   work_id=work_id, work_uuid=work_id, url=url,
                                   download_url='', long_url=url, digest=content,
                                   title=title, thumbnail_link=thumbnail_link,
                                   content=content, img_urls='', video_urls='',
                                   music_url='', music_author_name='',
                                   music_id='', music_name='', publish_time=publish_time,
                                   publish_day=publish_day, location_ip=ip_location, read_count=0,
                                   like_count=like_count, comment_count=comment_count,
                                   share_count=share_count, collect_count=collect_count,
                                   text_polarity=-1,
                                   record_time=TimeUtils.get_current_ts(),
                                   is_detail=1
                                   )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def generate_tiktok_article_v2_2(self, data):
        try:
            d = data.get("note_list")
            if len(d) == 0:
                return None

            user = data.get("user")
            author_name = user.get('name', '')
            author_id = user.get('userid', '')
            author_avatar = user.get('image', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            note_list = data.get("note_list")
            if len(note_list) == 0:
                return None

            note = note_list[0]
            work_id = note.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title', '')
            content = note.get('desc', '')
            collect_count = note.get('collected_count', '')
            comment_count = note.get('comments_count', '')
            share_count = note.get('share_count', '')
            like_count = note.get('likes', '')
            publish_time = TimeUtils.ts_to_str(note.get('create_time'))

            ip_location = note.get('ip_location', '')
            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")

            thumbnail_link = ''
            try:
                topics = note.get('images_list')
                topic = topics[0]
                thumbnail_link = topic.get("url", "")
            except Exception as e:
                pass

            return WorkDetailModel(_id=0, platform='xhs', author_id=author_id,
                                   author_identity=author_id, author_avatar=author_avatar,
                                   author_name=author_name, author_url=author_url,
                                   work_id=work_id, work_uuid=work_id, url=url,
                                   download_url='', long_url=url, digest=content,
                                   title=title, thumbnail_link=thumbnail_link,
                                   content=content, img_urls='', video_urls='',
                                   music_url='', music_author_name='',
                                   music_id='', music_name='', publish_time=publish_time,
                                   publish_day=publish_day, location_ip=ip_location, read_count=0,
                                   like_count=like_count, comment_count=comment_count,
                                   share_count=share_count, collect_count=collect_count,
                                   text_polarity=-1,
                                   record_time=TimeUtils.get_current_ts(),
                                   is_detail=1
                                   )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def _generate_qingbo_article(self, item: dict) -> Optional[WorkDetailModel]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = item.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return WorkDetailModel(_id=0, platform='xhs', author_id=author_id,
                                   author_identity=author_id, author_avatar=author_avatar,
                                   author_name=author_name, author_url=author_url,
                                   work_id=work_id, work_uuid=work_id, url=url,
                                   download_url='', long_url=url, digest=content,
                                   title=title, thumbnail_link=thumbnail_link,
                                   content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                                   music_id='', music_name='', publish_time=publish_time,
                                   publish_day=publish_day, location_ip='', read_count=0,
                                   like_count=like_count, comment_count=comment_count,
                                   share_count=share_count, collect_count=collect_count,
                                   text_polarity=-1,
                                   record_time=TimeUtils.get_current_ts(),
                                   is_detail=1
                                   )
        except Exception as e:
            self.logger_.error(f"_generate_qingbo_article {StringUtils.obj_2_json_string(item)}, {e}")
            return None
