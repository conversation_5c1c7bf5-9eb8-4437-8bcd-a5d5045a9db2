# -*- coding: utf-8 -*-
"""
Adapter module for social media platform APIs.

This module provides handlers for various social media platforms:
- <PERSON><PERSON><PERSON> (TikTok China): Detail and search handlers
- <PERSON><PERSON><PERSON> (Little Red Book): Detail and comment handlers
- <PERSON>aishou: Detail handler
- TikTok: Search handler
"""

from .douyin_detail_handler import DouyinDetailHandler
from .douyin_search_handler import DouyinSearchHandler
from .kuaishou_detail_handler import KuaiShouDetailHandler
from .wxvideo_handler import WxVideoHandler
from .xhs_detail_handler import XhsDetailHandler
from .xhs_comment_handler import XhsCommentHandler
from .tiktok_search_handler import TiktokSearchHandler

__all__ = [
    "DouyinDetailHandler",
    "DouyinSearchHandler",
    "KuaiShouDetailHandler",
    "XhsDetailHandler",
    "XhsCommentHandler",
    "TiktokSearchHandler",
    "WxVideoHandler"
]