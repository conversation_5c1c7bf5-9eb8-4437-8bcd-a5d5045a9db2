# JSS API Extension

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PyPI Version](https://img.shields.io/pypi/v/jss-api-extend.svg)](https://pypi.org/project/jss-api-extend/)

JSS API Extension 是一个综合性的 Python 包，为社交媒体平台 API（抖音、小红书、快手）和钉钉多维表集成提供统一的接口。

## 功能特性

### 🎯 社交媒体平台适配器 (adapter)
- **抖音 (Douyin)**: 作品详情获取、搜索功能
- **小红书 (Xiaohongshu)**: 作品详情获取、评论获取
- **快手 (Kuaishou)**: 作品详情获取
- **TikTok**: 搜索功能

### 📊 钉钉多维表集成 (bitable)
- 钉钉多维表数据管理和通知服务
- 支持批量数据操作
- 消息推送和告警功能
- 多公司配置支持

### 🔌 第三方服务客户端 (client)
- Tikhub API 客户端（抖音、小红书、快手）
- 飞书多维表客户端
- 内部服务 API 客户端
- 钉钉集成客户端

### 🛠 工具库 (utils)
- HTTP 请求工具
- 字符串处理工具
- 时间处理工具
- URL 处理工具
- 地区代码转换器
- 限流器等实用工具

## 安装

### 使用 pip 安装（推荐）

```bash
pip install jss-api-extend
```

这将自动安装所有必需的依赖项：
- `requests>=2.25.0` - HTTP 请求库
- `python-dateutil>=2.8.0` - 日期时间处理
- `tikhub>=1.0.0` - TikHub API 客户端
- `lark-oapi>=1.0.0` - 飞书开放平台 API
- `alibabacloud-dingtalk>=2.0.0` - 钉钉开放平台 API
- `alibabacloud-tea-util>=0.3.0` - 阿里云 Tea 工具库
- `alibabacloud-tea-openapi>=0.3.0` - 阿里云 Tea OpenAPI

### 从源码安装

```bash
# 克隆仓库
git clone https://github.com/jinsuosuo/jss-api-extend.git
cd jss-api-extend

# 安装包及其依赖
pip install .

# 或者安装为可编辑模式（开发用）
pip install -e .
```

## 快速开始

### 1. 抖音作品详情获取

```python
from jss_api_extend import DouyinDetailHandler
from jss_api_extend.common import Env

# 初始化日志
logger = Env().get_main_logger()

# 创建抖音详情处理器
douyin_handler = DouyinDetailHandler(logger)

# 获取作品详情
work_id = "7521675067002162467"
work_detail = await douyin_handler.query_article_detail(work_id)
print(work_detail)
```

### 2. 小红书作品详情和评论获取

```python
from jss_api_extend import XhsDetailHandler, XhsCommentHandler
from jss_api_extend.common import Env

logger = Env().get_main_logger()

# 获取作品详情
xhs_handler = XhsDetailHandler(logger)
note_id = "6863e1450000000024008d2b"
note_detail = xhs_handler.query_article_detail(note_id)

# 获取评论
comment_handler = XhsCommentHandler(logger)
success, all_comments, top_comments = comment_handler.query_article_comment(
    note_id, count=50
)
```

### 3. 钉钉多维表操作

```python
from jss_api_extend import DingTalkBitableNotifier
from jss_api_extend.common import Env

logger = Env().get_main_logger()

# 创建钉钉多维表通知器
notifier = DingTalkBitableNotifier(
    logger_=logger,
    app_key="your_app_key",
    app_secret="your_app_secret",
    agent_id="your_agent_id"
)

# 查询多维表数据
dentry_uuid = "your_table_uuid"
sheet_name = "your_sheet_name"
records = notifier.list_bitable_data_by_api_filter(
    dentry_uuid, sheet_name, max_update_times=5
)
```

### 4. 快手作品详情获取

```python
from jss_api_extend import KuaiShouDetailHandler
from jss_api_extend.common import Env

logger = Env().get_main_logger()
ks_handler = KuaiShouDetailHandler(logger)

work_url = "https://www.kuaishou.com/short-video/3xk4g2yryfjpin6"
work_detail = ks_handler.query_article_detail_app_v1(work_url)
```

## 配置

### 环境变量配置

在使用之前，需要配置相关的 API 密钥和配置信息：

```bash
# Tikhub API 配置
export TIKHUB_BASE_URL="https://api.tikhub.io"
export TIKHUB_API_KEY="your_tikhub_api_key"

# 钉钉配置
export DINGTALK_APP_KEY="your_dingtalk_app_key"
export DINGTALK_APP_SECRET="your_dingtalk_app_secret"
export DINGTALK_AGENT_ID="your_dingtalk_agent_id"

# 飞书配置
export LARK_APP_ID="your_lark_app_id"
export LARK_APP_SECRET="your_lark_app_secret"
```

### 配置文件

你也可以通过配置文件来设置这些参数。创建一个 `config.json` 文件：

```json
{
  "tikhub": {
    "base_url": "https://api.tikhub.io",
    "api_key": "your_tikhub_api_key"
  },
  "dingtalk": {
    "app_key": "your_dingtalk_app_key",
    "app_secret": "your_dingtalk_app_secret",
    "agent_id": "your_dingtalk_agent_id"
  },
  "lark": {
    "app_id": "your_lark_app_id",
    "app_secret": "your_lark_app_secret"
  }
}
```

## API 文档

### Adapter 模块

#### DouyinDetailHandler
- `query_article_detail(work_id)`: 获取抖音作品详情
- `query_article_detail_by_tikhub(work_id)`: 通过 Tikhub API 获取作品详情

#### XhsDetailHandler
- `query_article_detail(note_id)`: 获取小红书笔记详情
- `query_article_detail_by_tikhub(note_id)`: 通过 Tikhub API 获取笔记详情

#### XhsCommentHandler
- `query_article_comment(note_id, count=50)`: 获取小红书笔记评论

#### KuaiShouDetailHandler
- `query_article_detail_app_v1(work_url)`: 获取快手作品详情

### Bitable 模块

#### DingTalkBitableNotifier
- `list_bitable_data_by_api_filter()`: 查询多维表数据
- `batch_create_bitable_data()`: 批量创建数据
- `batch_update_bitable_data()`: 批量更新数据
- `send_dingtalk_message()`: 发送钉钉消息

### Client 模块

#### TikhubDouyinClient
- `get_douyin_video_data()`: 获取抖音视频数据
- `search_douyin_videos()`: 搜索抖音视频

#### TikhubXhsClient
- `get_xhs_note_data()`: 获取小红书笔记数据
- `get_xhs_note_comments()`: 获取小红书笔记评论

## 开发

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/jinsuosuo/jss-api-extend.git
cd jss-api-extend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装开发依赖
pip install -e ".[dev]"
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_adapter.py

# 运行测试并生成覆盖率报告
pytest --cov=jss_api_extend
```

### 代码格式化

```bash
# 格式化代码
black src/
isort src/

# 检查代码风格
flake8 src/
mypy src/
```

## 错误处理

包中的所有 API 调用都包含适当的错误处理。主要异常类型：

- `ServiceException`: 服务相关异常
- `InterruptedException`: 中断异常
- `ConnectionError`: 网络连接异常
- `TimeoutError`: 请求超时异常

```python
from jss_api_extend import DouyinDetailHandler
from jss_api_extend.common import ServiceException

try:
    handler = DouyinDetailHandler(logger)
    result = await handler.query_article_detail(work_id)
except ServiceException as e:
    print(f"服务异常: {e}")
except Exception as e:
    print(f"未知异常: {e}")
```

## 限制和注意事项

1. **API 限制**: 各平台 API 都有调用频率限制，请合理使用
2. **数据时效性**: 社交媒体数据更新频繁，获取的数据可能存在延迟
3. **合规使用**: 请遵守各平台的使用条款和数据使用规范
4. **网络环境**: 某些 API 可能需要特定的网络环境才能访问

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

- **项目主页**: https://github.com/jinsuosuo/jss-api-extend
- **问题反馈**: https://github.com/jinsuosuo/jss-api-extend/issues
- **邮箱**: <EMAIL>

## 更新日志

### v0.1.0 (2024-07-30)
- 初始版本发布
- 支持抖音、小红书、快手平台 API
- 钉钉多维表集成功能
- 完整的工具库和客户端支持

---

**注意**: 本包仅供学习和研究使用，请遵守相关平台的使用条款。