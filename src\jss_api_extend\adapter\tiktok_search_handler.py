# -*- coding: utf-8 -*-

import time

from dateutil import parser
from tikhub import Client

from ..client.tikhub_douyin_client import TikhubDouyinClient
from ..config.tikhub_config import tikhub_config
from models.work_detail_model import WorkDetailModel
from ..utils.region_code_converter import RegionCodeConverter
from ..utils.string_utils import StringUtils
from ..utils.time_utils import TimeUtils


class TiktokSearchHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url=tikhub_config.get('base_url'),
                             api_key=tikhub_config.get("api_key"),
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self._converter = RegionCodeConverter()

        self.tikhub_dy_client = TikhubDouyinClient(logger_=logger_)

    async def search_article_list(self, keyword, count, sort_type=0, publish_time=0):
        index = 0
        offset = 0
        article_list = []

        while True:
            index = index + 1
            if index > 30:
                break

            search_result = await self._search_keyword_by_tikhub(
                keyword, offset=offset, pagesize=20, sort_type=sort_type, publish_time=publish_time)
            if search_result is None:
                break

            offset = search_result.get("cursor")
            data = search_result.get("data")

            for note in data:
                article = self._generate_tiktok_article(note)
                if article is not None:
                    article_list.append(article)

            if len(article_list) > count:
                break

        return article_list

    async def _search_keyword_by_tikhub(self, keyword: str, offset: int, pagesize: int,
                                  publish_time: int, sort_type: int) -> dict | None:
        article = await self._fetch_general_search_v1_inner(keyword, offset, pagesize, publish_time, sort_type)
        if article is None:
            time.sleep(1)
            article = await self._fetch_general_search_v1_inner(keyword, offset, pagesize, publish_time, sort_type)

        if article is None:
            time.sleep(1)
            article = await self._fetch_general_search_v1_inner(keyword, offset, pagesize, publish_time, sort_type)

        if article is None:
            return None

        return article

    async def _fetch_general_search_v1_inner(self, keyword: str, offset: int = 0, pagesize: int = 20,
                                       publish_time: int = 0, sort_type: int = 0) -> dict | None:
        """
        keyword: 关键词
        offset: 偏移量
        pagesize: 数量
        sort_type:
            0-相关度，1-最多点赞
        publish_time:
            0-不限制，1-最近一天，7-最近一周，30-最近一个月，90-最近三个月，180-最近半年
        """
        result = await self.client.TikTokAppV3.fetch_general_search_result(
            keyword, offset, pagesize, sort_type, publish_time)
        if not result or "data" not in result:
            return None

        code = result.get("code")
        if code != 200:
            return None

        return result.get("data")

    def _generate_tiktok_article(self, data):
        try:
            note = data.get("aweme_info")
            if note is None:
                return None

            work_id = self._parse_json_str(note, "aweme_id")
            if note is None:
                return None

            collect_count = comment_count = share_count = like_count = 0
            publish_day = ''

            user = note.get("author")
            author_name = self._parse_json_str(user, 'nickname')
            author_id = self._parse_json_str(user, 'uid')
            author_uuid = self._parse_json_str(user, 'sec_uid')
            author_url = "https://www.tiktok.com/@" + self._parse_json_str(user, 'unique_id')

            author_avatar = ''
            try:
                avatar = user.get("avatar_thumb")
                url_list = avatar.get("url_list")
                author_avatar = url_list[0]
            except Exception as e1:
                print(e1)

            url = author_url + "/video/" + work_id

            title = note.get('desc', '')
            content = note.get('desc', '')

            statistics = note.get('statistics')
            if statistics is not None:
                collect_count = statistics.get('collect_count', 0)
                comment_count = statistics.get('comment_count', 0)
                share_count = statistics.get('share_count', 0)
                like_count = statistics.get('digg_count', 0)

            """
            city_code = note.get('city', '')
            ip_location = self._convert_code_to_name(city_code)
            """

            publish_time = TimeUtils.ts_to_str(note.get('create_time'))
            try:
                date_obj = parser.parse(publish_time)
                publish_day = date_obj.strftime("%Y-%m-%d")
            except Exception as e2:
                print(e2)

            thumbnail_link = ''
            video_url = ''
            try:
                video = note.get('video')
                cover = video.get('cover')
                cover_url_list = cover.get("url_list")
                thumbnail_link = cover_url_list[0]

                play_addr = video.get("play_addr")
                video_play_url_list = play_addr.get("url_list")
                for video_play_url in video_play_url_list:
                    if "amemv.com/aweme/v1/play" in video_play_url:
                        video_url = video_play_url
                        break

            except Exception as e:
                pass

            return WorkDetailModel(_id=0, platform='tiktok', author_id=author_id,
                                   author_identity=author_uuid, author_avatar=author_avatar,
                                   author_name=author_name, author_url=author_url,
                                   work_id=work_id, work_uuid=work_id, url=url,
                                   download_url='', long_url=url, digest=content,
                                   title=title, thumbnail_link=thumbnail_link,
                                   content=content, img_urls=thumbnail_link, video_urls=video_url,
                                   music_url='', music_author_name='',
                                   music_id='', music_name='', publish_time=publish_time,
                                   publish_day=publish_day, location_ip='', read_count=0,
                                   like_count=like_count, comment_count=comment_count,
                                   share_count=share_count, collect_count=collect_count,
                                   text_polarity=-1,
                                   record_time=TimeUtils.get_current_ts(),
                                   is_detail=1
                                   )

        except Exception as e:
            self.logger_.error(f"_generate_tiktok_article {StringUtils.obj_2_json_string(data)}, {e}")

    def _convert_code_to_name(self, code):
        if code is None:
            return ""

        d = self._converter.convert(code)
        province = d.get("province", "")
        city = d.get("city", "")

        if len(city) > 0:
            return city

        return province

    def _parse_json_str(self, obj, key):
        if obj is None:
            return ""

        return obj.get(key, "")

