from datetime import datetime

from tikhub import Client

from jss_api_extend import TikhubDouyinClient
from jss_api_extend.adapter.models.work_detail_model import UseWorkModel
from jss_api_extend.config import tikhub_config
from jss_api_extend.utils import TimeUtils


class DouyinUserHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url=tikhub_config.get('base_url'),
                             api_key=tikhub_config.get("api_key"),
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self.tikhub_client = TikhubDouyinClient(logger_)



    def _query_work_tracing_by_tikhub(self, sec_uid: str, start_time int):
        """
        账号历史作品追溯 从 start_time ~ 当前
        注意 最前页为置顶作品，要跳过置顶作品, 然后向后翻页直到出现时间 < start_time，停止并返回作品列表
        :param sec_uid:
        :param count: 需要获取多少个作品
        :return:
        """
        response = self.tikhub_client.fetch_user_post_videos(sec_uid)
    


class TikHubDouyinUserWorkDetailHelper(object):

    @staticmethod
    def parse_work_detail(json_data):
        """
        解析JSON数据并返回一个WorkDetailModel实例。

        Args:
            json_data (dict): 从JSON文件加载的字典数据。

        Returns:
            WorkDetailModel: 包含解析后数据的模型实例。
        """
        # 安全地获取嵌套字典中的值
        def safe_get(data, keys, default=None):
            for key in keys:
                if isinstance(data, dict):
                    data = data.get(key)
                else:
                    return default
            return data if data is not None else default
        try:
            # 提取作者信息
            author_info = json_data.get('author', {})
            author_id = author_info.get('sec_uid')
            author_identity = author_info.get('sec_uid')
            author_avatar = safe_get(author_info, ['avatar_thumb', 'url_list', 0])
            author_name = author_info.get('nickname')
            author_url = f"https://www.douyin.com/user/{author_info.get('sec_uid')}" if author_info.get('sec_uid') else None

            # 提取作品信息
            work_id = json_data.get('aweme_id')
            url = json_data.get('share_url')
            title = json_data.get('desc')
            content = json_data.get('desc')

            # 提取统计数据
            statistics = json_data.get('statistics', {})
            like_count = statistics.get('digg_count')
            comment_count = statistics.get('comment_count')
            share_count = statistics.get('share_count')
            collect_count = statistics.get('collect_count')
            read_count = statistics.get('play_count') # play_count 经常用作阅读/播放数

            # 提取媒体链接
            video_info = json_data.get('video', {})
            thumbnail_link = safe_get(video_info, ['origin_cover', 'url_list', 0])
            video_urls = safe_get(video_info, ['play_addr', 'url_list'], [])
            # 检查下载是否被允许
            download_url = None
            if safe_get(json_data, ['video_control', 'allow_download'], False):
                download_url = safe_get(video_info, ['download_addr', 'url_list', 0])


            # 提取音乐信息
            music_info = json_data.get('music', {})
            music_url = safe_get(music_info, ['play_url', 'url_list', 0])
            music_author_name = music_info.get('author')
            music_id = music_info.get('id')
            music_name = music_info.get('title')

            # 处理时间戳
            publish_timestamp = json_data.get('create_time')
            publish_time = None
            publish_day = None
            if publish_timestamp:
                dt_object = datetime.fromtimestamp(publish_timestamp)
                publish_time = dt_object.strftime("%Y-%m-%d %H:%M:%S")
                publish_day = dt_object.strftime("%Y-%m-%d")

            is_top = json_data.get('is_top', 0)

            # 实例化模型
            work_detail = UseWorkModel(
                platform="douyin",  # 根据URL推断平台为抖音
                author_id=author_id,
                author_identity=author_identity,
                author_avatar=author_avatar,
                author_name=author_name,
                author_url=author_url,
                work_id=work_id,
                work_uuid=work_id, # 使用 work_id 作为 uuid
                url=url,
                download_url=download_url,
                long_url=url, # 使用 share_url 作为 long_url
                digest=title,
                title=title,
                thumbnail_link=thumbnail_link,
                content=content,
                img_urls=json_data.get('images') or [], # 视频作品通常没有图片
                video_urls=video_urls,
                music_url=music_url,
                music_author_name=music_author_name,
                music_id=music_id,
                music_name=music_name,
                publish_time=publish_time,
                publish_day=publish_day,
                location_ip=json_data.get('ip_attribution'),
                read_count=read_count,
                like_count=like_count,
                comment_count=comment_count,
                share_count=share_count,
                collect_count=collect_count,
                is_detail=1,
                is_top=is_top,
                record_time=TimeUtils.get_current_ts()
            )

            return work_detail
        except Exception as e:
            raise e