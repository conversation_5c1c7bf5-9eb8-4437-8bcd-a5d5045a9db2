from tikhub import Client

from jss_api_extend import TikhubDouyinClient
from jss_api_extend.config import tikhub_config


class DouyinUserHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url=tikhub_config.get('base_url'),
                             api_key=tikhub_config.get("api_key"),
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self.tik_hub_client = TikhubDouyinClient(logger_)



    def query_user_video_by_tikhub(self, sec_uid: str):
        self.client.DouyinAppV3.
